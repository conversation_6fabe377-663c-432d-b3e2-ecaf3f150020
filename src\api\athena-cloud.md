---
title: athena-cloud
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# athena-cloud

Base URLs:

# Authentication

# wiki

<a id="opIdupdateDoc"></a>

## PUT 文件管理文档编辑(带id)

PUT /wiki/doc/manage

文件管理文档编辑(带id)

> Body 请求参数

```json
{
  "id": 0,
  "title": "string",
  "keyword": "string",
  "ossId": 0,
  "url": "string",
  "attName": "string",
  "summary": "string",
  "spaceId": "string",
  "tagBo": [
    {
      "tagId": 0,
      "tagName": "string",
      "tagCode": "string",
      "tagType": "string",
      "description": "string",
      "parentId": 0,
      "sort": 0
    }
  ],
  "ownerName": "string",
  "fsUrl": "string",
  "customizeTag": [
    {
      "tagId": 0,
      "tagName": "string",
      "tagCode": "string",
      "tagType": "string",
      "description": "string",
      "parentId": 0,
      "sort": 0
    }
  ],
  "type": "string",
  "downloadType": "string",
  "collaboratorList": [
    {
      "createBy": 0,
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": 0,
      "updateTime": "2019-08-24T14:15:22Z",
      "params": {
        "property1": {},
        "property2": {}
      },
      "tenantId": "string",
      "id": 0,
      "docId": 0,
      "collaboratorType": "string",
      "unionCode": "string",
      "permissionType": "string",
      "delFlag": "string"
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|[WikiDocumentEditDocBo](#schemawikidocumenteditdocbo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": true
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RBoolean](#schemarboolean)|

<a id="opIdaddDocList"></a>

## POST 本地批量文件新增

POST /wiki/doc/manage

本地批量文件新增

> Body 请求参数

```json
[
  {
    "id": 0,
    "title": "string",
    "keyword": "string",
    "ossId": 0,
    "url": "string",
    "attName": "string",
    "summary": "string",
    "spaceId": "string",
    "tagBo": [
      {
        "tagId": 0,
        "tagName": "string",
        "tagCode": "string",
        "tagType": "string",
        "description": "string",
        "parentId": 0,
        "sort": 0
      }
    ],
    "ownerName": "string",
    "fsUrl": "string",
    "customizeTag": [
      {
        "tagId": 0,
        "tagName": "string",
        "tagCode": "string",
        "tagType": "string",
        "description": "string",
        "parentId": 0,
        "sort": 0
      }
    ],
    "type": "string",
    "downloadType": "string",
    "collaboratorList": [
      {
        "createBy": 0,
        "createTime": "2019-08-24T14:15:22Z",
        "updateBy": 0,
        "updateTime": "2019-08-24T14:15:22Z",
        "params": {
          "property1": {},
          "property2": {}
        },
        "tenantId": "string",
        "id": 0,
        "docId": 0,
        "collaboratorType": "string",
        "unionCode": "string",
        "permissionType": "string",
        "delFlag": "string"
      }
    ]
  }
]
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|[WikiDocumentEditDocBo](#schemawikidocumenteditdocbo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": true
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RBoolean](#schemarboolean)|

<a id="opIdupdateStatus"></a>

## PUT 文件管理文档启用/禁用:文件批量更新状态

PUT /wiki/doc/manage/status

文件管理文档启用/禁用:文件批量更新状态

> Body 请求参数

```json
{
  "ids": [
    0
  ],
  "status": 0,
  "downloadType": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|[WikiDocumentStatusBo](#schemawikidocumentstatusbo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": true
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RBoolean](#schemarboolean)|

<a id="opIdupdateDownloadType"></a>

## PUT 文件管理文档批量更新下载类型
 支持的下载类型：
 1 - 禁止下载
 2 - 审批流下载
 3 - 直接下载

PUT /wiki/doc/manage/download-type

文件管理文档批量更新下载类型
 支持的下载类型：
 1 - 禁止下载
 2 - 审批流下载
 3 - 直接下载

> Body 请求参数

```json
{
  "ids": [
    0
  ],
  "status": 0,
  "downloadType": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|[WikiDocumentStatusBo](#schemawikidocumentstatusbo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": true
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RBoolean](#schemarboolean)|

<a id="opIdsyncGs"></a>

## POST syncGs

POST /wiki/sync/hr/{tenantId}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tenantId|path|string| 是 |none|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RVoid](#schemarvoid)|

<a id="opIduploadFile"></a>

## POST 文件管理:上传文件

POST /wiki/doc/manage/upload

文件管理:上传文件

> Body 请求参数

```json
{
  "file": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "ossId": 0,
    "name": "string",
    "url": "string",
    "originalUrl": "string"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RRemoteFileVo](#schemarremotefilevo)|

<a id="opIdaddFsDoc"></a>

## POST addFsDoc

POST /wiki/doc/manage/fs

> Body 请求参数

```json
{
  "id": 0,
  "title": "string",
  "keyword": "string",
  "ossId": 0,
  "url": "string",
  "attName": "string",
  "summary": "string",
  "spaceId": "string",
  "tagBo": [
    {
      "tagId": 0,
      "tagName": "string",
      "tagCode": "string",
      "tagType": "string",
      "description": "string",
      "parentId": 0,
      "sort": 0
    }
  ],
  "ownerName": "string",
  "fsUrl": "string",
  "customizeTag": [
    {
      "tagId": 0,
      "tagName": "string",
      "tagCode": "string",
      "tagType": "string",
      "description": "string",
      "parentId": 0,
      "sort": 0
    }
  ],
  "type": "string",
  "downloadType": "string",
  "collaboratorList": [
    {
      "createBy": 0,
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": 0,
      "updateTime": "2019-08-24T14:15:22Z",
      "params": {
        "property1": {},
        "property2": {}
      },
      "tenantId": "string",
      "id": 0,
      "docId": 0,
      "collaboratorType": "string",
      "unionCode": "string",
      "permissionType": "string",
      "delFlag": "string"
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|[WikiDocumentEditDocBo](#schemawikidocumenteditdocbo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": true
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RBoolean](#schemarboolean)|

<a id="opIdgetDocDetails"></a>

## GET 文件管理详情

GET /wiki/doc/manage/{id}

文件管理详情

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer(int64)| 是 |ID|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "createBy": 0,
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": 0,
    "updateTime": "2019-08-24T14:15:22Z",
    "params": {
      "property1": {},
      "property2": {}
    },
    "id": 0,
    "title": "string",
    "type": "string",
    "keyword": "string",
    "ossId": 0,
    "url": "string",
    "attName": "string",
    "summary": "string",
    "status": 0,
    "unionId": 0,
    "version": "string",
    "spaceId": 0,
    "ownerName": "string",
    "createByName": "string",
    "tagIds": [
      0
    ],
    "tagNames": "string",
    "customizeTag": [
      {
        "tagName": "string",
        "highlightTag": "string",
        "id": 0,
        "tagCode": "string",
        "tagType": "string",
        "description": "string",
        "levelPath": "string",
        "level": "string",
        "parentId": 0,
        "sort": 0,
        "tagNamePathList": [
          "string"
        ]
      }
    ],
    "downloadType": "string",
    "collaboratorList": [
      {
        "docId": 0,
        "collaboratorType": "string",
        "userId": 0,
        "userName": "string",
        "nickName": "string",
        "deptId": 0,
        "deptCode": "string",
        "deptName": "string",
        "delFlag": "string"
      }
    ]
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RWikiDocumentManageVo](#schemarwikidocumentmanagevo)|

<a id="opIdpptToPdf"></a>

## GET pptToPdf

GET /wiki/doc/manage/pptToPdf

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ossId|query|integer(int64)| 是 |none|
|documentId|query|integer(int64)| 是 |none|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "ossId": 0,
    "name": "string",
    "url": "string",
    "originalUrl": "string"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RRemoteFileVo](#schemarremotefilevo)|

<a id="opIdpage_3"></a>

## GET 文件管理分页列表

GET /wiki/doc/manage/page

文件管理分页列表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer(int64)| 否 |标题|
|title|query|string| 否 |标题|
|spaceId|query|string| 否 |空间ID|
|createByName|query|string| 否 |发布人|
|tagIds|query|array[string]| 否 |筛选标签id|
|wikiTagList|query|array[object]| 否 |none|
|userId|query|integer(int64)| 否 |none|
|pageSize|query|integer(int32)| 否 |分页大小|
|pageNum|query|integer(int32)| 否 |当前页数|
|orderByColumn|query|string| 否 |排序列|
|isAsc|query|string| 否 |排序的方向desc或者asc|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "total": 0,
  "rows": [
    {
      "createBy": 0,
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": 0,
      "updateTime": "2019-08-24T14:15:22Z",
      "params": {
        "property1": {},
        "property2": {}
      },
      "id": 0,
      "title": "string",
      "type": "string",
      "keyword": "string",
      "ossId": 0,
      "url": "string",
      "attName": "string",
      "summary": "string",
      "status": 0,
      "unionId": 0,
      "version": "string",
      "spaceId": 0,
      "ownerName": "string",
      "createByName": "string",
      "tagIds": [
        0
      ],
      "tagNames": "string",
      "customizeTag": [
        {
          "tagName": "string",
          "highlightTag": "string",
          "id": 0,
          "tagCode": "string",
          "tagType": "string",
          "description": "string",
          "levelPath": "string",
          "level": "string",
          "parentId": 0,
          "sort": 0,
          "tagNamePathList": [
            "string"
          ]
        }
      ],
      "downloadType": "string",
      "collaboratorList": [
        {
          "docId": 0,
          "collaboratorType": "string",
          "userId": 0,
          "userName": "string",
          "nickName": "string",
          "deptId": 0,
          "deptCode": "string",
          "deptName": "string",
          "delFlag": "string"
        }
      ]
    }
  ],
  "code": 0,
  "msg": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[TableDataInfoWikiDocumentManageVo](#schematabledatainfowikidocumentmanagevo)|

# wiki/点赞收藏接口

<a id="opIdupdate_1"></a>

## PUT 收藏/取消收藏(点赞/取消点赞)

PUT /wiki/favorite/{type}/{docId}

收藏/取消收藏(点赞/取消点赞)

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|type|path|integer(int32)| 是 |1收藏  2点赞|
|docId|path|integer(int64)| 是 |文章id|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": true
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RBoolean](#schemarboolean)|

<a id="opIdlist_1"></a>

## POST 收藏列表

POST /wiki/favorite/list

收藏列表

> Body 请求参数

```json
{
  "pageSize": 0,
  "pageNum": 0,
  "orderByColumn": "string",
  "isAsc": "string",
  "keyWord": "string",
  "tagIdList": [
    0
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|[WikiFavouriteBo](#schemawikifavouritebo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "list": [
      {
        "id": 0,
        "docId": 0,
        "title": "string",
        "summary": "string",
        "wikiTagVoList": [
          {
            "tagName": null,
            "highlightTag": null,
            "id": null,
            "tagCode": null,
            "tagType": null,
            "description": null,
            "levelPath": null,
            "level": null,
            "parentId": null,
            "sort": null,
            "tagNamePathList": null
          }
        ],
        "createTime": "2019-08-24T14:15:22Z",
        "parentTagVoList": [
          {
            "tagName": null,
            "highlightTag": null,
            "id": null,
            "tagCode": null,
            "tagType": null,
            "description": null,
            "levelPath": null,
            "level": null,
            "parentId": null,
            "sort": null,
            "tagNamePathList": null
          }
        ],
        "customizeTag": [
          {
            "tagName": null,
            "highlightTag": null,
            "id": null,
            "tagCode": null,
            "tagType": null,
            "description": null,
            "levelPath": null,
            "level": null,
            "parentId": null,
            "sort": null,
            "tagNamePathList": null
          }
        ],
        "downloadType": "string"
      }
    ],
    "pageNum": 0,
    "pageSize": 0,
    "size": 0,
    "startRow": 0,
    "endRow": 0,
    "pages": 0,
    "prePage": 0,
    "nextPage": 0,
    "hasPreviousPage": true,
    "hasNextPage": true,
    "navigatePages": 0,
    "navigatePageNums": [
      0
    ],
    "navigateFirstPage": 0,
    "navigateLastPage": 0,
    "firstPage": true,
    "lastPage": true
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[REsPageInfoWikiDocFavouriteVo](#schemarespageinfowikidocfavouritevo)|

<a id="opIdfindFavouriteSize"></a>

## GET 获取点赞收藏总数

GET /wiki/favorite/size

获取点赞收藏总数

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "likeSize": 0,
    "collectSize": 0
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RWikiFavouriteSizeVo](#schemarwikifavouritesizevo)|

# wiki/空间接口

<a id="opIdupdateSpace"></a>

## POST 更新空间状态

POST /wiki/space/update

更新空间状态

> Body 请求参数

```json
{
  "id": 0,
  "name": "string",
  "code": "string",
  "status": "string",
  "isAdmin": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|[WikiSpaceBo](#schemawikispacebo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": true
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RBoolean](#schemarboolean)|

<a id="opIdfindList"></a>

## POST 查询空间列表

POST /wiki/space/list

查询空间列表

> Body 请求参数

```json
{
  "id": 0,
  "name": "string",
  "code": "string",
  "status": "string",
  "isAdmin": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|[WikiSpaceBo](#schemawikispacebo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "spaceId": "string",
      "spaceName": "string",
      "spaceCode": "string",
      "spaceStatus": "string",
      "roleList": [
        {
          "roleId": "string",
          "roleCode": "string",
          "roleName": "string"
        }
      ],
      "userTotal": 0,
      "isVisible": true
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RListUserRoleVo](#schemarlistuserrolevo)|

<a id="opIdinsertSpace"></a>

## POST 新增空间

POST /wiki/space/insert

新增空间

> Body 请求参数

```json
{
  "id": 0,
  "name": "string",
  "code": "string",
  "status": "string",
  "isAdmin": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|[WikiSpaceBo](#schemawikispacebo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": true
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RBoolean](#schemarboolean)|

# wiki/文章接口

<a id="opIdfindSpecialDocumentList"></a>

## POST 根据专栏查询专栏文章

POST /wiki/document/special/page

根据专栏查询专栏文章

> Body 请求参数

```json
{
  "pageSize": 0,
  "pageNum": 0,
  "orderByColumn": "string",
  "isAsc": "string",
  "specialId": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|[WikiSpecialBo](#schemawikispecialbo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "list": [
      {
        "id": 0,
        "ossId": 0,
        "url": "string",
        "type": "string",
        "title": "string",
        "highlightTitle": "string",
        "summary": "string",
        "highlightSummary": "string",
        "status": "string",
        "delFlag": "string",
        "isLike": true,
        "isCollect": true,
        "wikiTagVoList": [
          {
            "tagName": null,
            "highlightTag": null,
            "id": null,
            "tagCode": null,
            "tagType": null,
            "description": null,
            "levelPath": null,
            "level": null,
            "parentId": null,
            "sort": null,
            "tagNamePathList": null
          }
        ],
        "ownerName": "string",
        "parentTagVoList": [
          {
            "tagName": null,
            "highlightTag": null,
            "id": null,
            "tagCode": null,
            "tagType": null,
            "description": null,
            "levelPath": null,
            "level": null,
            "parentId": null,
            "sort": null,
            "tagNamePathList": null
          }
        ],
        "operationTime": "string",
        "spaceName": "string",
        "spaceId": 0,
        "customizeTag": [
          {
            "tagName": null,
            "highlightTag": null,
            "id": null,
            "tagCode": null,
            "tagType": null,
            "description": null,
            "levelPath": null,
            "level": null,
            "parentId": null,
            "sort": null,
            "tagNamePathList": null
          }
        ],
        "downloadType": "string"
      }
    ],
    "pageNum": 0,
    "pageSize": 0,
    "size": 0,
    "startRow": 0,
    "endRow": 0,
    "pages": 0,
    "prePage": 0,
    "nextPage": 0,
    "hasPreviousPage": true,
    "hasNextPage": true,
    "navigatePages": 0,
    "navigatePageNums": [
      0
    ],
    "navigateFirstPage": 0,
    "navigateLastPage": 0,
    "firstPage": true,
    "lastPage": true
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[REsPageInfoWikiDocumentVo](#schemarespageinfowikidocumentvo)|

<a id="opIdpage"></a>

## POST 搜索列表

POST /wiki/document/page

搜索列表

> Body 请求参数

```json
{
  "pageSize": 0,
  "pageNum": 0,
  "orderByColumn": "string",
  "isAsc": "string",
  "keyWord": "string",
  "tagIdList": [
    0
  ],
  "spaceId": 0,
  "documentIdList": [
    0
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|[WikiDocumentBo](#schemawikidocumentbo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "list": [
      {
        "id": 0,
        "ossId": 0,
        "url": "string",
        "type": "string",
        "title": "string",
        "highlightTitle": "string",
        "summary": "string",
        "highlightSummary": "string",
        "status": "string",
        "delFlag": "string",
        "isLike": true,
        "isCollect": true,
        "wikiTagVoList": [
          {
            "tagName": null,
            "highlightTag": null,
            "id": null,
            "tagCode": null,
            "tagType": null,
            "description": null,
            "levelPath": null,
            "level": null,
            "parentId": null,
            "sort": null,
            "tagNamePathList": null
          }
        ],
        "ownerName": "string",
        "parentTagVoList": [
          {
            "tagName": null,
            "highlightTag": null,
            "id": null,
            "tagCode": null,
            "tagType": null,
            "description": null,
            "levelPath": null,
            "level": null,
            "parentId": null,
            "sort": null,
            "tagNamePathList": null
          }
        ],
        "operationTime": "string",
        "spaceName": "string",
        "spaceId": 0,
        "customizeTag": [
          {
            "tagName": null,
            "highlightTag": null,
            "id": null,
            "tagCode": null,
            "tagType": null,
            "description": null,
            "levelPath": null,
            "level": null,
            "parentId": null,
            "sort": null,
            "tagNamePathList": null
          }
        ],
        "downloadType": "string"
      }
    ],
    "pageNum": 0,
    "pageSize": 0,
    "size": 0,
    "startRow": 0,
    "endRow": 0,
    "pages": 0,
    "prePage": 0,
    "nextPage": 0,
    "hasPreviousPage": true,
    "hasNextPage": true,
    "navigatePages": 0,
    "navigatePageNums": [
      0
    ],
    "navigateFirstPage": 0,
    "navigateLastPage": 0,
    "firstPage": true,
    "lastPage": true
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[REsPageInfoWikiDocumentVo](#schemarespageinfowikidocumentvo)|

<a id="opIdfindByDocumentIdList"></a>

## POST 根据文章id集合查询文章详情

POST /wiki/document/listByIds

根据文章id集合查询文章详情

> Body 请求参数

```json
{
  "pageSize": 0,
  "pageNum": 0,
  "orderByColumn": "string",
  "isAsc": "string",
  "keyWord": "string",
  "tagIdList": [
    0
  ],
  "spaceId": 0,
  "documentIdList": [
    0
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|[WikiDocumentBo](#schemawikidocumentbo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "id": 0,
      "ossId": 0,
      "url": "string",
      "type": "string",
      "title": "string",
      "highlightTitle": "string",
      "summary": "string",
      "highlightSummary": "string",
      "status": "string",
      "delFlag": "string",
      "isLike": true,
      "isCollect": true,
      "wikiTagVoList": [
        {
          "tagName": "string",
          "highlightTag": "string",
          "id": 0,
          "tagCode": "string",
          "tagType": "string",
          "description": "string",
          "levelPath": "string",
          "level": "string",
          "parentId": 0,
          "sort": 0,
          "tagNamePathList": [
            "string"
          ]
        }
      ],
      "ownerName": "string",
      "parentTagVoList": [
        {
          "tagName": "string",
          "highlightTag": "string",
          "id": 0,
          "tagCode": "string",
          "tagType": "string",
          "description": "string",
          "levelPath": "string",
          "level": "string",
          "parentId": 0,
          "sort": 0,
          "tagNamePathList": [
            "string"
          ]
        }
      ],
      "operationTime": "string",
      "spaceName": "string",
      "spaceId": 0,
      "customizeTag": [
        {
          "tagName": "string",
          "highlightTag": "string",
          "id": 0,
          "tagCode": "string",
          "tagType": "string",
          "description": "string",
          "levelPath": "string",
          "level": "string",
          "parentId": 0,
          "sort": 0,
          "tagNamePathList": [
            "string"
          ]
        }
      ],
      "downloadType": "string"
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RListWikiDocumentVo](#schemarlistwikidocumentvo)|

<a id="opIdadd"></a>

## POST 下载申请

POST /wiki/document/downLoad/apply

下载申请

> Body 请求参数

```json
{
  "applyType": "string",
  "applyTime": "2019-08-24T14:15:22Z",
  "applicant": "string",
  "applicantCode": "string",
  "postCode": "string",
  "postName": "string",
  "deptCode": "string",
  "deptName": "string",
  "ancestors": "string",
  "applyNodeInfo": {
    "property1": [
      {
        "userId": 0,
        "empCode": "string",
        "empName": "string"
      }
    ],
    "property2": [
      {
        "userId": 0,
        "empCode": "string",
        "empName": "string"
      }
    ]
  },
  "pcLink": "string",
  "appLink": "string",
  "processId": "string",
  "appCode": "string",
  "tenantId": "string",
  "docId": 0,
  "spaceId": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|[WikiApplyBo](#schemawikiapplybo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RString](#schemarstring)|

<a id="opIdfindById"></a>

## GET 查询文章详情

GET /wiki/document/{documentId}

查询文章详情

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|documentId|path|integer(int64)| 是 |none|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "id": 0,
    "ossId": 0,
    "url": "string",
    "type": "string",
    "title": "string",
    "highlightTitle": "string",
    "summary": "string",
    "highlightSummary": "string",
    "status": "string",
    "delFlag": "string",
    "isLike": true,
    "isCollect": true,
    "wikiTagVoList": [
      {
        "tagName": "string",
        "highlightTag": "string",
        "id": 0,
        "tagCode": "string",
        "tagType": "string",
        "description": "string",
        "levelPath": "string",
        "level": "string",
        "parentId": 0,
        "sort": 0,
        "tagNamePathList": [
          "string"
        ]
      }
    ],
    "ownerName": "string",
    "parentTagVoList": [
      {
        "tagName": "string",
        "highlightTag": "string",
        "id": 0,
        "tagCode": "string",
        "tagType": "string",
        "description": "string",
        "levelPath": "string",
        "level": "string",
        "parentId": 0,
        "sort": 0,
        "tagNamePathList": [
          "string"
        ]
      }
    ],
    "operationTime": "string",
    "spaceName": "string",
    "spaceId": 0,
    "customizeTag": [
      {
        "tagName": "string",
        "highlightTag": "string",
        "id": 0,
        "tagCode": "string",
        "tagType": "string",
        "description": "string",
        "levelPath": "string",
        "level": "string",
        "parentId": 0,
        "sort": 0,
        "tagNamePathList": [
          "string"
        ]
      }
    ],
    "downloadType": "string"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RWikiDocumentVo](#schemarwikidocumentvo)|

<a id="opIdfindSpecialList"></a>

## GET 查询专栏信息

GET /wiki/document/special

查询专栏信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "specialId": 0,
      "specialName": "string"
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RListWikiSpecialVo](#schemarlistwikispecialvo)|

<a id="opIdfindLatestUpload"></a>

## GET 查询最新上传的文章

GET /wiki/document/new

查询最新上传的文章

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "id": 0,
      "ossId": 0,
      "url": "string",
      "type": "string",
      "title": "string",
      "highlightTitle": "string",
      "summary": "string",
      "highlightSummary": "string",
      "status": "string",
      "delFlag": "string",
      "isLike": true,
      "isCollect": true,
      "wikiTagVoList": [
        {
          "tagName": "string",
          "highlightTag": "string",
          "id": 0,
          "tagCode": "string",
          "tagType": "string",
          "description": "string",
          "levelPath": "string",
          "level": "string",
          "parentId": 0,
          "sort": 0,
          "tagNamePathList": [
            "string"
          ]
        }
      ],
      "ownerName": "string",
      "parentTagVoList": [
        {
          "tagName": "string",
          "highlightTag": "string",
          "id": 0,
          "tagCode": "string",
          "tagType": "string",
          "description": "string",
          "levelPath": "string",
          "level": "string",
          "parentId": 0,
          "sort": 0,
          "tagNamePathList": [
            "string"
          ]
        }
      ],
      "operationTime": "string",
      "spaceName": "string",
      "spaceId": 0,
      "customizeTag": [
        {
          "tagName": "string",
          "highlightTag": "string",
          "id": 0,
          "tagCode": "string",
          "tagType": "string",
          "description": "string",
          "levelPath": "string",
          "level": "string",
          "parentId": 0,
          "sort": 0,
          "tagNamePathList": [
            "string"
          ]
        }
      ],
      "downloadType": "string"
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RListWikiDocumentVo](#schemarlistwikidocumentvo)|

<a id="opIdgetApprovalStatus"></a>

## GET 根据文档ID查询审批流状态

GET /wiki/document/apply-status/{documentId}

根据文档ID查询审批流状态

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|documentId|path|integer(int64)| 是 |文档ID|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "documentId": 0,
    "hasApprovalFlow": true,
    "approvalStatus": "string"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RWikiDocumentApprovalStatusVo](#schemarwikidocumentapprovalstatusvo)|

# wiki/热门接口

<a id="opIdpage_2"></a>

## GET page_2

GET /wiki/tag/page

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageSize|query|integer(int32)| 否 |分页大小|
|pageNum|query|integer(int32)| 否 |当前页数|
|orderByColumn|query|string| 否 |排序列|
|isAsc|query|string| 否 |排序的方向desc或者asc|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "rows": [
      {
        "docId": 0,
        "title": "string",
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "code": 0,
    "msg": "string"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RTableDataInfoWikiHotDocVo](#schemartabledatainfowikihotdocvo)|

# wiki/标签接口

<a id="opIdupdate"></a>

## PUT 属性字典更新（pc）

PUT /wiki/tag

属性字典更新（pc）

> Body 请求参数

```json
{
  "tagId": 0,
  "tagName": "string",
  "tagCode": "string",
  "tagType": "string",
  "description": "string",
  "parentId": 0,
  "sort": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|[WikiTagBo](#schemawikitagbo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": true
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RBoolean](#schemarboolean)|

<a id="opIdinsert"></a>

## POST 属性字典新增（pc）

POST /wiki/tag

属性字典新增（pc）

> Body 请求参数

```json
{
  "tagId": 0,
  "tagName": "string",
  "tagCode": "string",
  "tagType": "string",
  "description": "string",
  "parentId": 0,
  "sort": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|[WikiTagBo](#schemawikitagbo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": true
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RBoolean](#schemarboolean)|

<a id="opIdgetTagWikiCount"></a>

## GET 根据标签父id查询文章数

GET /wiki/tag/wiki/count/{parentId}

根据标签父id查询文章数

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|parentId|path|integer(int64)| 是 |none|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "tagId": 0,
      "count": 0
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RListTagWikiCountVo](#schemarlisttagwikicountvo)|

<a id="opIdgetChildrenNode"></a>

## GET 属性字典 获取子节点（pc）

GET /wiki/tag/node/{parentId}

属性字典 获取子节点（pc）

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|parentId|path|integer(int64)| 是 |第一级默认为0|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "tagName": "string",
      "highlightTag": "string",
      "id": 0,
      "tagCode": "string",
      "tagType": "string",
      "description": "string",
      "levelPath": "string",
      "level": "string",
      "parentId": 0,
      "sort": 0,
      "tagNamePathList": [
        "string"
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RListWikiTagVo](#schemarlistwikitagvo)|

<a id="opIddel"></a>

## DELETE 属性字典删除（pc）

DELETE /wiki/tag/{id}

属性字典删除（pc）

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer(int64)| 是 |属性ID|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": true
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RBoolean](#schemarboolean)|

<a id="opIdgetChildrenNodeAll"></a>

## GET 属性字典 获取所有节点（树形结构）

GET /wiki/tag/node/all

属性字典 获取所有节点（树形结构）

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "tagName": "string",
      "highlightTag": "string",
      "id": 0,
      "tagCode": "string",
      "tagType": "string",
      "description": "string",
      "levelPath": "string",
      "level": "string",
      "parentId": 0,
      "sort": 0,
      "tagNamePathList": [
        "string"
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RListWikiTagVo](#schemarlistwikitagvo)|

<a id="opIdgetChildrenNodeBySpaceId"></a>

## GET 根据空间id获取标签树

GET /wiki/tag/node/all/{spaceId}

根据空间id获取标签树

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|spaceId|path|integer(int64)| 是 |none|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "tagName": "string",
      "highlightTag": "string",
      "id": 0,
      "tagCode": "string",
      "tagType": "string",
      "description": "string",
      "levelPath": "string",
      "level": "string",
      "parentId": 0,
      "sort": 0,
      "tagNamePathList": [
        "string"
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RListWikiTagVo](#schemarlistwikitagvo)|

# wiki/空间角色

<a id="opIdinsert_1"></a>

## POST 角色对象新增（pc）

POST /wiki/space/role/object/insert

角色对象新增（pc）

> Body 请求参数

```json
{
  "spaceId": 0,
  "roleId": 0,
  "spaceRoleObjectList": [
    {
      "objectType": "string",
      "unionCode": "string"
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|[WikiSpaceRoleInsertBo](#schemawikispaceroleinsertbo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RVoid](#schemarvoid)|

<a id="opIdlist"></a>

## POST 空间角色查询

POST /wiki/space/role/list

空间角色查询

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageSize|query|integer(int32)| 否 |分页大小|
|pageNum|query|integer(int32)| 否 |当前页数|
|orderByColumn|query|string| 否 |排序列|
|isAsc|query|string| 否 |排序的方向desc或者asc|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "rows": [
      {
        "id": 0,
        "roleName": "string",
        "roleCode": "string"
      }
    ],
    "code": 0,
    "msg": "string"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RTableDataInfoWikiSpaceRoleConfigVo](#schemartabledatainfowikispaceroleconfigvo)|

<a id="opIdlist_2"></a>

## GET 获取全部人员

GET /wiki/space/role/user

获取全部人员

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|userId|query|integer(int64)| 否 |none|
|userName|query|string| 否 |none|
|nickName|query|string| 否 |none|
|phoneNumber|query|string| 否 |none|
|sex|query|string| 否 |none|
|deptName|query|string| 否 |none|
|pageSize|query|integer(int32)| 否 |分页大小|
|pageNum|query|integer(int32)| 否 |当前页数|
|orderByColumn|query|string| 否 |排序列|
|isAsc|query|string| 否 |排序的方向desc或者asc|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "rows": [
      {
        "userId": 0,
        "userName": "string",
        "nickName": "string",
        "phoneNumber": "string",
        "sex": "string",
        "deptName": "string"
      }
    ],
    "code": 0,
    "msg": "string"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RTableDataInfoSysUserBo](#schemartabledatainfosysuserbo)|

<a id="opIdlist_3"></a>

## GET 获取角色对象（人员、部门）（pc分页）

GET /wiki/space/role/object/list

获取角色对象（人员、部门）（pc分页）

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|spaceId|query|integer(int64)| 否 |空间id|
|roleId|query|integer(int64)| 否 |角色id|
|objectType|query|string| 否 |对象类型(0-部门，1-人)|
|pageSize|query|integer(int32)| 否 |分页大小|
|pageNum|query|integer(int32)| 否 |当前页数|
|orderByColumn|query|string| 否 |排序列|
|isAsc|query|string| 否 |排序的方向desc或者asc|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "rows": [
      {
        "id": 0,
        "spaceId": 0,
        "roleId": 0,
        "objectType": "string",
        "userId": 0,
        "userName": "string",
        "nickName": "string",
        "deptId": 0,
        "deptCode": "string",
        "deptName": "string"
      }
    ],
    "code": 0,
    "msg": "string"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RTableDataInfoWikiSpaceRoleObjectVo](#schemartabledatainfowikispaceroleobjectvo)|

<a id="opIdlibList"></a>

## GET 获取部门列表

GET /wiki/space/role/dept/list

获取部门列表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|compCode|query|string| 否 |公司code|
|parentId|query|integer(int64)| 否 |父部门ID|
|keyWord|query|string| 否 |关键字搜索|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "deptId": 0,
      "parentId": 0,
      "parentName": "string",
      "ancestors": "string",
      "deptName": "string",
      "deptType": "string",
      "deptMgrNum": "string",
      "deptMgrName": "string",
      "orderNum": 0,
      "leader": 0,
      "leaderName": "string",
      "phone": "string",
      "email": "string",
      "status": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "parentDeptCode": "string",
      "deptCode": "string",
      "deptLevel": "string",
      "compCode": "string",
      "compName": "string",
      "isChildren": true
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RListSysDeptVo](#schemarlistsysdeptvo)|

<a id="opIddel_1"></a>

## DELETE 角色对象删除（pc）

DELETE /wiki/space/role/object/{id}

角色对象删除（pc）

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer(int64)| 是 |属性ID|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RVoid](#schemarvoid)|

# wiki/敏感词管理

<a id="opIdreplaceSensitiveWord"></a>

## POST 替换敏感词

POST /wiki/sensitive/replace

替换敏感词

> Body 请求参数

```json
{
  "txt": "string",
  "matchType": 0,
  "replaceChar": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|[SensitiveWordReplaceBO](#schemasensitivewordreplacebo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "replaceTxt": "string"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RSensitiveWordReplaceVO](#schemarsensitivewordreplacevo)|

<a id="opIdisContainSensitiveWord"></a>

## POST 是否包含敏感词(方法)

POST /wiki/sensitive/is-contain

是否包含敏感词(方法)

> Body 请求参数

```json
"string"
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|matchType|query|integer(int32)| 是 |匹配类型|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": true
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RBoolean](#schemarboolean)|

<a id="opIdsensitiveWordFiltering"></a>

## POST 获取敏感词内容

POST /wiki/sensitive/filtering

获取敏感词内容

> Body 请求参数

```json
"string"
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|matchType|query|integer(int32)| 是 |匹配类型|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    "string"
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RSetString](#schemarsetstring)|

<a id="opIdaddSensitiveWord"></a>

## POST PC 新增敏感词

POST /wiki/sensitive/add

PC 新增敏感词

> Body 请求参数

```json
{
  "id": 0,
  "content": "string",
  "createdEmpName": "string",
  "createTime": "2019-08-24T14:15:22Z"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|[SensitiveWordVo](#schemasensitivewordvo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[R](#schemar)|

<a id="opIdgetSensitiveWordPage"></a>

## GET PC 查询敏感词

GET /wiki/sensitive/query

PC 查询敏感词

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer(int32)| 否 |none|
|content|query|string| 否 |none|
|createdEmpName|query|string| 否 |none|
|createTime|query|string(date-time)| 否 |none|
|pageSize|query|integer(int32)| 否 |分页大小|
|pageNum|query|integer(int32)| 否 |当前页数|
|orderByColumn|query|string| 否 |排序列|
|isAsc|query|string| 否 |排序的方向desc或者asc|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "rows": [
      {
        "id": 0,
        "content": "string",
        "createdEmpName": "string",
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "code": 0,
    "msg": "string"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RTableDataInfoSensitiveWordVo](#schemartabledatainfosensitivewordvo)|

<a id="opIddeleteSensitiveWord"></a>

## DELETE PC 删除敏感词

DELETE /wiki/sensitive/{ids}

PC 删除敏感词

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ids|path|array[integer]| 是 |none|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[R](#schemar)|

# wiki/文件操作接口

<a id="opIdupload"></a>

## POST 上传OSS对象存储

POST /wiki/oss-file/upload

上传OSS对象存储

> Body 请求参数

```yaml
file: ""

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |文件|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "url": "string",
    "originalUrl": "string",
    "fileName": "string",
    "ossId": "string"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ROssFileUploadVo](#schemarossfileuploadvo)|

<a id="opIdupload_1"></a>

## POST 指定桶上传OSS对象存储

POST /wiki/oss-file/upload/{ossConfigKey}

指定桶上传OSS对象存储

> Body 请求参数

```yaml
file: ""

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ossConfigKey|path|string| 是 |none|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |文件|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "url": "string",
    "originalUrl": "string",
    "fileName": "string",
    "ossId": "string"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ROssFileUploadVo](#schemarossfileuploadvo)|

<a id="opIddetail"></a>

## GET 查看OSS对象存储(忽略租户)

GET /wiki/oss-file/{ossIds}

查看OSS对象存储(忽略租户)

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ossIds|path|array[integer]| 是 |OSS对象ID|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "rows": [
      {
        "ossId": 0,
        "fileName": "string",
        "originalName": "string",
        "fileSuffix": "string",
        "url": "string",
        "originalUrl": "string",
        "createTime": "2019-08-24T14:15:22Z",
        "createBy": 0,
        "service": "string"
      }
    ],
    "code": 0,
    "msg": "string"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RTableDataInfoOssFileBo](#schemartabledatainfoossfilebo)|

<a id="opIdremove"></a>

## DELETE 删除OSS对象存储(忽略租户)

DELETE /wiki/oss-file/{ossIds}

删除OSS对象存储(忽略租户)

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ossIds|path|array[integer]| 是 |OSS对象ID串|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RVoid](#schemarvoid)|

<a id="opIddetailTenant"></a>

## GET 查看OSS对象存储

GET /wiki/oss-file/tenant/{ossIds}

查看OSS对象存储

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ossIds|path|array[integer]| 是 |OSS对象ID|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "rows": [
      {
        "ossId": 0,
        "fileName": "string",
        "originalName": "string",
        "fileSuffix": "string",
        "url": "string",
        "originalUrl": "string",
        "createTime": "2019-08-24T14:15:22Z",
        "createBy": 0,
        "service": "string"
      }
    ],
    "code": 0,
    "msg": "string"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RTableDataInfoOssFileBo](#schemartabledatainfoossfilebo)|

<a id="opIdremoveTenant"></a>

## DELETE 删除OSS对象存储

DELETE /wiki/oss-file/tenant/{ossIds}

删除OSS对象存储

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ossIds|path|array[integer]| 是 |OSS对象ID串|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RVoid](#schemarvoid)|

<a id="opIddownload"></a>

## GET 下载OSS对象存储(忽略租户)

GET /wiki/oss-file/download/{ossId}

下载OSS对象存储(忽略租户)

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ossId|path|integer(int64)| 是 |OSS对象ID|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

<a id="opIddownloadTenant"></a>

## GET 下载OSS对象存储

GET /wiki/oss-file/download/tenant/{ossId}

下载OSS对象存储

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ossId|path|integer(int64)| 是 |OSS对象ID|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

<a id="opIddownloadTenant_3"></a>

## PUT 下载OSS对象存储

PUT /wiki/oss-file/download/tenant/{ossId}

下载OSS对象存储

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ossId|path|integer(int64)| 是 |OSS对象ID|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

<a id="opIddownloadTenant_2"></a>

## POST 下载OSS对象存储

POST /wiki/oss-file/download/tenant/{ossId}

下载OSS对象存储

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ossId|path|integer(int64)| 是 |OSS对象ID|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

<a id="opIddownloadTenant_5"></a>

## DELETE 下载OSS对象存储

DELETE /wiki/oss-file/download/tenant/{ossId}

下载OSS对象存储

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ossId|path|integer(int64)| 是 |OSS对象ID|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

<a id="opIddownloadTenant_6"></a>

## OPTIONS 下载OSS对象存储

OPTIONS /wiki/oss-file/download/tenant/{ossId}

下载OSS对象存储

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ossId|path|integer(int64)| 是 |OSS对象ID|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

<a id="opIddownloadTenant_1"></a>

## HEAD 下载OSS对象存储

HEAD /wiki/oss-file/download/tenant/{ossId}

下载OSS对象存储

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ossId|path|integer(int64)| 是 |OSS对象ID|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

<a id="opIddownloadTenant_4"></a>

## PATCH 下载OSS对象存储

PATCH /wiki/oss-file/download/tenant/{ossId}

下载OSS对象存储

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ossId|path|integer(int64)| 是 |OSS对象ID|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# wiki/最近浏览记录

<a id="opIdviewPage"></a>

## POST 最近浏览记录

POST /wiki/view/page

最近浏览记录

> Body 请求参数

```json
{
  "pageSize": 0,
  "pageNum": 0,
  "orderByColumn": "string",
  "isAsc": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|[PageQuery](#schemapagequery)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "list": [
      {
        "documentId": 0,
        "title": "string",
        "summary": "string",
        "status": "string",
        "wikiTagVoList": [
          {
            "tagName": null,
            "highlightTag": null,
            "id": null,
            "tagCode": null,
            "tagType": null,
            "description": null,
            "levelPath": null,
            "level": null,
            "parentId": null,
            "sort": null,
            "tagNamePathList": null
          }
        ],
        "viewTime": "string",
        "traffic": 0,
        "customizeTag": [
          {
            "tagName": null,
            "highlightTag": null,
            "id": null,
            "tagCode": null,
            "tagType": null,
            "description": null,
            "levelPath": null,
            "level": null,
            "parentId": null,
            "sort": null,
            "tagNamePathList": null
          }
        ],
        "coverUrl": "string"
      }
    ],
    "pageNum": 0,
    "pageSize": 0,
    "size": 0,
    "startRow": 0,
    "endRow": 0,
    "pages": 0,
    "prePage": 0,
    "nextPage": 0,
    "hasPreviousPage": true,
    "hasNextPage": true,
    "navigatePages": 0,
    "navigatePageNums": [
      0
    ],
    "navigateFirstPage": 0,
    "navigateLastPage": 0,
    "firstPage": true,
    "lastPage": true
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[REsPageInfoWikiDocumentViewVo](#schemarespageinfowikidocumentviewvo)|

<a id="opIdpage_1"></a>

## GET 最近浏览记录分页查询

GET /wiki/view/page

最近浏览记录分页查询

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageSize|query|integer(int32)| 否 |分页大小|
|pageNum|query|integer(int32)| 否 |当前页数|
|orderByColumn|query|string| 否 |排序列|
|isAsc|query|string| 否 |排序的方向desc或者asc|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "rows": [
      {
        "docId": 0,
        "title": "string",
        "wikiTagList": [
          {
            "tagName": null,
            "highlightTag": null,
            "id": null,
            "tagCode": null,
            "tagType": null,
            "description": null,
            "levelPath": null,
            "level": null,
            "parentId": null,
            "sort": null,
            "tagNamePathList": null
          }
        ],
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "code": 0,
    "msg": "string"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RTableDataInfoWikiDocViewRecordVo](#schemartabledatainfowikidocviewrecordvo)|

# wiki/操作日志接口

<a id="opIdqueryGenWikiLog"></a>

## POST 查询操作日志

POST /wiki/doc/log/operate/query

查询操作日志

> Body 请求参数

```json
{
  "content": "string",
  "empCode": "string",
  "logType": "string",
  "logTimeStart": "string",
  "logTimeEnd": "string",
  "systemTerminal": "string",
  "spaceName": "string",
  "roleName": "string",
  "empName": "string",
  "deptCode": "string",
  "deptName": "string",
  "pageSize": 0,
  "pageNum": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|[OperateLogQueryBo](#schemaoperatelogquerybo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "list": [
      {}
    ],
    "pageNum": 0,
    "pageSize": 0,
    "size": 0,
    "startRow": 0,
    "endRow": 0,
    "pages": 0,
    "prePage": 0,
    "nextPage": 0,
    "hasPreviousPage": true,
    "hasNextPage": true,
    "navigatePages": 0,
    "navigatePageNums": [
      0
    ],
    "navigateFirstPage": 0,
    "navigateLastPage": 0,
    "firstPage": true,
    "lastPage": true
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[REsPageInfoObject](#schemarespageinfoobject)|

<a id="opIdoperateAddLog"></a>

## POST 插入操作日志埋点

POST /wiki/doc/log/operate/insert

插入操作日志埋点

> Body 请求参数

```json
{
  "logType": "string",
  "content": "string",
  "systemTerminal": "string",
  "spaceName": "string",
  "roleName": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|[OperateLogBo](#schemaoperatelogbo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": true
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RBoolean](#schemarboolean)|

# wiki/日志

<a id="opIdqueryGenWikiLog"></a>

## POST genWiki查询日志

POST /wiki/log/wiki/query

genWiki查询日志

> Body 请求参数

```json
"string"
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|logSchema|query|string| 是 |none|
|pageSize|query|integer(int32)| 否 |分页大小|
|pageNum|query|integer(int32)| 否 |当前页数|
|orderByColumn|query|string| 否 |排序列|
|isAsc|query|string| 否 |排序的方向desc或者asc|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "list": [
      {}
    ],
    "pageNum": 0,
    "pageSize": 0,
    "size": 0,
    "startRow": 0,
    "endRow": 0,
    "pages": 0,
    "prePage": 0,
    "nextPage": 0,
    "hasPreviousPage": true,
    "hasNextPage": true,
    "navigatePages": 0,
    "navigatePageNums": [
      0
    ],
    "navigateFirstPage": 0,
    "navigateLastPage": 0,
    "firstPage": true,
    "lastPage": true
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[REsPageInfoObject](#schemarespageinfoobject)|

<a id="opIdqueryAuditLog"></a>

## POST audit查询日志

POST /wiki/log/audit/query

audit查询日志

> Body 请求参数

```json
"string"
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|logService|query|string| 否 |none|
|logSchema|query|string| 是 |none|
|pageSize|query|integer(int32)| 否 |分页大小|
|pageNum|query|integer(int32)| 否 |当前页数|
|orderByColumn|query|string| 否 |排序列|
|isAsc|query|string| 否 |排序的方向desc或者asc|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "list": [
      {}
    ],
    "pageNum": 0,
    "pageSize": 0,
    "size": 0,
    "startRow": 0,
    "endRow": 0,
    "pages": 0,
    "prePage": 0,
    "nextPage": 0,
    "hasPreviousPage": true,
    "hasNextPage": true,
    "navigatePages": 0,
    "navigatePageNums": [
      0
    ],
    "navigateFirstPage": 0,
    "navigateLastPage": 0,
    "firstPage": true,
    "lastPage": true
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[REsPageInfoObject](#schemarespageinfoobject)|

# wiki/FastGPT相关API控制器

<a id="opIdgetFileList"></a>

## POST 获取文件列表

POST /wiki/fast/v1/file/list

获取文件列表

> Body 请求参数

```json
{
  "parentId": "string",
  "searchKey": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|[FastFileListBo](#schemafastfilelistbo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "success": true,
  "message": "string",
  "data": [
    {
      "id": "string",
      "parentId": "string",
      "type": "string",
      "hasChild": true,
      "name": "string",
      "updateTime": "string",
      "createTime": "string"
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[FastApiResponseListFastFileVo](#schemafastapiresponselistfastfilevo)|

<a id="opIdgetFileUrl"></a>

## GET 获取文件URL

GET /wiki/fast/v1/file/read

获取文件URL

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer(int64)| 是 |文件ID|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "success": true,
  "message": "string",
  "data": {
    "url": "string"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[FastApiResponseFastFileUrlVo](#schemafastapiresponsefastfileurlvo)|

<a id="opIdgetFileDetail"></a>

## GET 获取文件详情

GET /wiki/fast/v1/file/detail

获取文件详情

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer(int64)| 是 |文件ID|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "success": true,
  "message": "string",
  "data": {
    "id": "string",
    "parentId": "string",
    "type": "string",
    "hasChild": true,
    "name": "string",
    "updateTime": "string",
    "createTime": "string"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[FastApiResponseFastFileVo](#schemafastapiresponsefastfilevo)|

<a id="opIdgetFileContent"></a>

## GET 获取文件内容

GET /wiki/fast/v1/file/content

获取文件内容

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer(int64)| 是 |文件ID|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "success": true,
  "message": "string",
  "data": {
    "title": "string",
    "content": "string",
    "previewUrl": "string"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[FastApiResponseFastFileContentVo](#schemafastapiresponsefastfilecontentvo)|

# 数据模型

<h2 id="tocS_SysUserBo">SysUserBo</h2>

<a id="schemasysuserbo"></a>
<a id="schema_SysUserBo"></a>
<a id="tocSsysuserbo"></a>
<a id="tocssysuserbo"></a>

```json
{
  "userId": 0,
  "userName": "string",
  "nickName": "string",
  "phoneNumber": "string",
  "sex": "string",
  "deptName": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|userId|integer(int64)|false|none||none|
|userName|string|false|none||none|
|nickName|string|false|none||none|
|phoneNumber|string|false|none||none|
|sex|string|false|none||none|
|deptName|string|false|none||none|

<h2 id="tocS_RVoid">RVoid</h2>

<a id="schemarvoid"></a>
<a id="schema_RVoid"></a>
<a id="tocSrvoid"></a>
<a id="tocsrvoid"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|object|false|none||数据对象|

<h2 id="tocS_SysDeptVo">SysDeptVo</h2>

<a id="schemasysdeptvo"></a>
<a id="schema_SysDeptVo"></a>
<a id="tocSsysdeptvo"></a>
<a id="tocssysdeptvo"></a>

```json
{
  "deptId": 0,
  "parentId": 0,
  "parentName": "string",
  "ancestors": "string",
  "deptName": "string",
  "deptType": "string",
  "deptMgrNum": "string",
  "deptMgrName": "string",
  "orderNum": 0,
  "leader": 0,
  "leaderName": "string",
  "phone": "string",
  "email": "string",
  "status": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "parentDeptCode": "string",
  "deptCode": "string",
  "deptLevel": "string",
  "compCode": "string",
  "compName": "string",
  "isChildren": true
}

```

部门视图对象 sys_dept

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|deptId|integer(int64)|false|none||部门id|
|parentId|integer(int64)|false|none||父部门id|
|parentName|string|false|none||父部门名称|
|ancestors|string|false|none||祖级列表|
|deptName|string|false|none||部门名称|
|deptType|string|false|none||部门类型|
|deptMgrNum|string|false|none||部门负责人工号|
|deptMgrName|string|false|none||部门负责人姓名|
|orderNum|integer(int32)|false|none||显示顺序|
|leader|integer(int64)|false|none||负责人ID|
|leaderName|string|false|none||负责人|
|phone|string|false|none||联系电话|
|email|string|false|none||邮箱|
|status|string|false|none||部门状态（0正常 1停用）|
|createTime|string(date-time)|false|none||创建时间|
|parentDeptCode|string|false|none||父级部门编码|
|deptCode|string|false|none||部门编号|
|deptLevel|string|false|none||部门编号|
|compCode|string|false|none||公司编号|
|compName|string|false|none||公司名称|
|isChildren|boolean|false|none||none|

<h2 id="tocS_PageQuery">PageQuery</h2>

<a id="schemapagequery"></a>
<a id="schema_PageQuery"></a>
<a id="tocSpagequery"></a>
<a id="tocspagequery"></a>

```json
{
  "pageSize": 0,
  "pageNum": 0,
  "orderByColumn": "string",
  "isAsc": "string"
}

```

分页查询实体类

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageSize|integer(int32)|false|none||分页大小|
|pageNum|integer(int32)|false|none||当前页数|
|orderByColumn|string|false|none||排序列|
|isAsc|string|false|none||排序的方向desc或者asc|

<h2 id="tocS_RListSysDeptVo">RListSysDeptVo</h2>

<a id="schemarlistsysdeptvo"></a>
<a id="schema_RListSysDeptVo"></a>
<a id="tocSrlistsysdeptvo"></a>
<a id="tocsrlistsysdeptvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "deptId": 0,
      "parentId": 0,
      "parentName": "string",
      "ancestors": "string",
      "deptName": "string",
      "deptType": "string",
      "deptMgrNum": "string",
      "deptMgrName": "string",
      "orderNum": 0,
      "leader": 0,
      "leaderName": "string",
      "phone": "string",
      "email": "string",
      "status": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "parentDeptCode": "string",
      "deptCode": "string",
      "deptLevel": "string",
      "compCode": "string",
      "compName": "string",
      "isChildren": true
    }
  ]
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|[[SysDeptVo](#schemasysdeptvo)]|false|none||数据对象|

<h2 id="tocS_RString">RString</h2>

<a id="schemarstring"></a>
<a id="schema_RString"></a>
<a id="tocSrstring"></a>
<a id="tocsrstring"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": "string"
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|string|false|none||数据对象|

<h2 id="tocS_R">R</h2>

<a id="schemar"></a>
<a id="schema_R"></a>
<a id="tocSr"></a>
<a id="tocsr"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|object|false|none||数据对象|

<h2 id="tocS_RBoolean">RBoolean</h2>

<a id="schemarboolean"></a>
<a id="schema_RBoolean"></a>
<a id="tocSrboolean"></a>
<a id="tocsrboolean"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": true
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|boolean|false|none||数据对象|

<h2 id="tocS_RRemoteFileVo">RRemoteFileVo</h2>

<a id="schemarremotefilevo"></a>
<a id="schema_RRemoteFileVo"></a>
<a id="tocSrremotefilevo"></a>
<a id="tocsrremotefilevo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "ossId": 0,
    "name": "string",
    "url": "string",
    "originalUrl": "string"
  }
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|[RemoteFileVo](#schemaremotefilevo)|false|none||文件信息|

<h2 id="tocS_RemoteFileVo">RemoteFileVo</h2>

<a id="schemaremotefilevo"></a>
<a id="schema_RemoteFileVo"></a>
<a id="tocSremotefilevo"></a>
<a id="tocsremotefilevo"></a>

```json
{
  "ossId": 0,
  "name": "string",
  "url": "string",
  "originalUrl": "string"
}

```

文件信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|ossId|integer(int64)|false|none||oss主键|
|name|string|false|none||文件名称|
|url|string|false|none||文件地址|
|originalUrl|string|false|none||原始URL地址|

<h2 id="tocS_TableDataInfoSysUserBo">TableDataInfoSysUserBo</h2>

<a id="schematabledatainfosysuserbo"></a>
<a id="schema_TableDataInfoSysUserBo"></a>
<a id="tocStabledatainfosysuserbo"></a>
<a id="tocstabledatainfosysuserbo"></a>

```json
{
  "total": 0,
  "rows": [
    {
      "userId": 0,
      "userName": "string",
      "nickName": "string",
      "phoneNumber": "string",
      "sex": "string",
      "deptName": "string"
    }
  ],
  "code": 0,
  "msg": "string"
}

```

表格分页数据对象

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||总记录数|
|rows|[[SysUserBo](#schemasysuserbo)]|false|none||列表数据|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|

<h2 id="tocS_OssFileUploadVo">OssFileUploadVo</h2>

<a id="schemaossfileuploadvo"></a>
<a id="schema_OssFileUploadVo"></a>
<a id="tocSossfileuploadvo"></a>
<a id="tocsossfileuploadvo"></a>

```json
{
  "url": "string",
  "originalUrl": "string",
  "fileName": "string",
  "ossId": "string"
}

```

上传对象信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|url|string|false|none||URL地址|
|originalUrl|string|false|none||原始URL地址|
|fileName|string|false|none||文件名|
|ossId|string|false|none||对象存储主键|

<h2 id="tocS_ROssFileUploadVo">ROssFileUploadVo</h2>

<a id="schemarossfileuploadvo"></a>
<a id="schema_ROssFileUploadVo"></a>
<a id="tocSrossfileuploadvo"></a>
<a id="tocsrossfileuploadvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "url": "string",
    "originalUrl": "string",
    "fileName": "string",
    "ossId": "string"
  }
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|[OssFileUploadVo](#schemaossfileuploadvo)|false|none||上传对象信息|

<h2 id="tocS_OssFileBo">OssFileBo</h2>

<a id="schemaossfilebo"></a>
<a id="schema_OssFileBo"></a>
<a id="tocSossfilebo"></a>
<a id="tocsossfilebo"></a>

```json
{
  "ossId": 0,
  "fileName": "string",
  "originalName": "string",
  "fileSuffix": "string",
  "url": "string",
  "originalUrl": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "createBy": 0,
  "service": "string"
}

```

OSS对象存储视图对象 sys_oss

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|ossId|integer(int64)|false|none||对象存储主键|
|fileName|string|false|none||文件名|
|originalName|string|false|none||原名|
|fileSuffix|string|false|none||文件后缀名|
|url|string|false|none||URL地址|
|originalUrl|string|false|none||原始URL地址|
|createTime|string(date-time)|false|none||创建时间|
|createBy|integer(int64)|false|none||上传人|
|service|string|false|none||服务商|

<h2 id="tocS_RTableDataInfoOssFileBo">RTableDataInfoOssFileBo</h2>

<a id="schemartabledatainfoossfilebo"></a>
<a id="schema_RTableDataInfoOssFileBo"></a>
<a id="tocSrtabledatainfoossfilebo"></a>
<a id="tocsrtabledatainfoossfilebo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "rows": [
      {
        "ossId": 0,
        "fileName": "string",
        "originalName": "string",
        "fileSuffix": "string",
        "url": "string",
        "originalUrl": "string",
        "createTime": "2019-08-24T14:15:22Z",
        "createBy": 0,
        "service": "string"
      }
    ],
    "code": 0,
    "msg": "string"
  }
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|[TableDataInfoOssFileBo](#schematabledatainfoossfilebo)|false|none||表格分页数据对象|

<h2 id="tocS_TableDataInfoOssFileBo">TableDataInfoOssFileBo</h2>

<a id="schematabledatainfoossfilebo"></a>
<a id="schema_TableDataInfoOssFileBo"></a>
<a id="tocStabledatainfoossfilebo"></a>
<a id="tocstabledatainfoossfilebo"></a>

```json
{
  "total": 0,
  "rows": [
    {
      "ossId": 0,
      "fileName": "string",
      "originalName": "string",
      "fileSuffix": "string",
      "url": "string",
      "originalUrl": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "createBy": 0,
      "service": "string"
    }
  ],
  "code": 0,
  "msg": "string"
}

```

表格分页数据对象

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||总记录数|
|rows|[[OssFileBo](#schemaossfilebo)]|false|none||列表数据|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|

<h2 id="tocS_SensitiveWordReplaceBO">SensitiveWordReplaceBO</h2>

<a id="schemasensitivewordreplacebo"></a>
<a id="schema_SensitiveWordReplaceBO"></a>
<a id="tocSsensitivewordreplacebo"></a>
<a id="tocssensitivewordreplacebo"></a>

```json
{
  "txt": "string",
  "matchType": 0,
  "replaceChar": "string"
}

```

签到/退表单

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|txt|string|true|none||敏感词|
|matchType|integer(int32)|true|none||匹配类型|
|replaceChar|string|true|none||代替词|

<h2 id="tocS_RSensitiveWordReplaceVO">RSensitiveWordReplaceVO</h2>

<a id="schemarsensitivewordreplacevo"></a>
<a id="schema_RSensitiveWordReplaceVO"></a>
<a id="tocSrsensitivewordreplacevo"></a>
<a id="tocsrsensitivewordreplacevo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "replaceTxt": "string"
  }
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|[SensitiveWordReplaceVO](#schemasensitivewordreplacevo)|false|none||none|

<h2 id="tocS_SensitiveWordReplaceVO">SensitiveWordReplaceVO</h2>

<a id="schemasensitivewordreplacevo"></a>
<a id="schema_SensitiveWordReplaceVO"></a>
<a id="tocSsensitivewordreplacevo"></a>
<a id="tocssensitivewordreplacevo"></a>

```json
{
  "replaceTxt": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|replaceTxt|string|false|none||none|

<h2 id="tocS_RSetString">RSetString</h2>

<a id="schemarsetstring"></a>
<a id="schema_RSetString"></a>
<a id="tocSrsetstring"></a>
<a id="tocsrsetstring"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    "string"
  ]
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|[string]|false|none||数据对象|

<h2 id="tocS_SensitiveWordVo">SensitiveWordVo</h2>

<a id="schemasensitivewordvo"></a>
<a id="schema_SensitiveWordVo"></a>
<a id="tocSsensitivewordvo"></a>
<a id="tocssensitivewordvo"></a>

```json
{
  "id": 0,
  "content": "string",
  "createdEmpName": "string",
  "createTime": "2019-08-24T14:15:22Z"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int32)|false|none||none|
|content|string|false|none||none|
|createdEmpName|string|false|none||none|
|createTime|string(date-time)|false|none||none|

<h2 id="tocS_RTableDataInfoSensitiveWordVo">RTableDataInfoSensitiveWordVo</h2>

<a id="schemartabledatainfosensitivewordvo"></a>
<a id="schema_RTableDataInfoSensitiveWordVo"></a>
<a id="tocSrtabledatainfosensitivewordvo"></a>
<a id="tocsrtabledatainfosensitivewordvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "rows": [
      {
        "id": 0,
        "content": "string",
        "createdEmpName": "string",
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "code": 0,
    "msg": "string"
  }
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|[TableDataInfoSensitiveWordVo](#schematabledatainfosensitivewordvo)|false|none||表格分页数据对象|

<h2 id="tocS_TableDataInfoSensitiveWordVo">TableDataInfoSensitiveWordVo</h2>

<a id="schematabledatainfosensitivewordvo"></a>
<a id="schema_TableDataInfoSensitiveWordVo"></a>
<a id="tocStabledatainfosensitivewordvo"></a>
<a id="tocstabledatainfosensitivewordvo"></a>

```json
{
  "total": 0,
  "rows": [
    {
      "id": 0,
      "content": "string",
      "createdEmpName": "string",
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "code": 0,
  "msg": "string"
}

```

表格分页数据对象

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||总记录数|
|rows|[[SensitiveWordVo](#schemasensitivewordvo)]|false|none||列表数据|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|

<h2 id="tocS_WikiDocumentBo">WikiDocumentBo</h2>

<a id="schemawikidocumentbo"></a>
<a id="schema_WikiDocumentBo"></a>
<a id="tocSwikidocumentbo"></a>
<a id="tocswikidocumentbo"></a>

```json
{
  "pageSize": 0,
  "pageNum": 0,
  "orderByColumn": "string",
  "isAsc": "string",
  "keyWord": "string",
  "tagIdList": [
    0
  ],
  "spaceId": 0,
  "documentIdList": [
    0
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageSize|integer(int32)|false|none||分页大小|
|pageNum|integer(int32)|false|none||当前页数|
|orderByColumn|string|false|none||排序列|
|isAsc|string|false|none||排序的方向desc或者asc|
|keyWord|string|false|none||关键字|
|tagIdList|[integer]|false|none||筛选标签|
|spaceId|integer(int64)|false|none||空间ID|
|documentIdList|[integer]|false|none||文档ID列表|

<h2 id="tocS_EsPageInfoWikiDocumentVo">EsPageInfoWikiDocumentVo</h2>

<a id="schemaespageinfowikidocumentvo"></a>
<a id="schema_EsPageInfoWikiDocumentVo"></a>
<a id="tocSespageinfowikidocumentvo"></a>
<a id="tocsespageinfowikidocumentvo"></a>

```json
{
  "total": 0,
  "list": [
    {
      "id": 0,
      "ossId": 0,
      "url": "string",
      "type": "string",
      "title": "string",
      "highlightTitle": "string",
      "summary": "string",
      "highlightSummary": "string",
      "status": "string",
      "delFlag": "string",
      "isLike": true,
      "isCollect": true,
      "wikiTagVoList": [
        {
          "tagName": "string",
          "highlightTag": "string",
          "id": 0,
          "tagCode": "string",
          "tagType": "string",
          "description": "string",
          "levelPath": "string",
          "level": "string",
          "parentId": 0,
          "sort": 0,
          "tagNamePathList": [
            "string"
          ]
        }
      ],
      "ownerName": "string",
      "parentTagVoList": [
        {
          "tagName": "string",
          "highlightTag": "string",
          "id": 0,
          "tagCode": "string",
          "tagType": "string",
          "description": "string",
          "levelPath": "string",
          "level": "string",
          "parentId": 0,
          "sort": 0,
          "tagNamePathList": [
            "string"
          ]
        }
      ],
      "operationTime": "string",
      "spaceName": "string",
      "spaceId": 0,
      "customizeTag": [
        {
          "tagName": "string",
          "highlightTag": "string",
          "id": 0,
          "tagCode": "string",
          "tagType": "string",
          "description": "string",
          "levelPath": "string",
          "level": "string",
          "parentId": 0,
          "sort": 0,
          "tagNamePathList": [
            "string"
          ]
        }
      ],
      "downloadType": "string"
    }
  ],
  "pageNum": 0,
  "pageSize": 0,
  "size": 0,
  "startRow": 0,
  "endRow": 0,
  "pages": 0,
  "prePage": 0,
  "nextPage": 0,
  "hasPreviousPage": true,
  "hasNextPage": true,
  "navigatePages": 0,
  "navigatePageNums": [
    0
  ],
  "navigateFirstPage": 0,
  "navigateLastPage": 0,
  "firstPage": true,
  "lastPage": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|list|[[WikiDocumentVo](#schemawikidocumentvo)]|false|none||none|
|pageNum|integer(int32)|false|none||none|
|pageSize|integer(int32)|false|none||none|
|size|integer(int32)|false|none||none|
|startRow|integer(int32)|false|none||none|
|endRow|integer(int32)|false|none||none|
|pages|integer(int32)|false|none||none|
|prePage|integer(int32)|false|none||none|
|nextPage|integer(int32)|false|none||none|
|hasPreviousPage|boolean|false|none||none|
|hasNextPage|boolean|false|none||none|
|navigatePages|integer(int32)|false|none||none|
|navigatePageNums|[integer]|false|none||none|
|navigateFirstPage|integer(int32)|false|none||none|
|navigateLastPage|integer(int32)|false|none||none|
|firstPage|boolean|false|none||none|
|lastPage|boolean|false|none||none|

<h2 id="tocS_REsPageInfoWikiDocumentVo">REsPageInfoWikiDocumentVo</h2>

<a id="schemarespageinfowikidocumentvo"></a>
<a id="schema_REsPageInfoWikiDocumentVo"></a>
<a id="tocSrespageinfowikidocumentvo"></a>
<a id="tocsrespageinfowikidocumentvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "list": [
      {
        "id": 0,
        "ossId": 0,
        "url": "string",
        "type": "string",
        "title": "string",
        "highlightTitle": "string",
        "summary": "string",
        "highlightSummary": "string",
        "status": "string",
        "delFlag": "string",
        "isLike": true,
        "isCollect": true,
        "wikiTagVoList": [
          {
            "tagName": null,
            "highlightTag": null,
            "id": null,
            "tagCode": null,
            "tagType": null,
            "description": null,
            "levelPath": null,
            "level": null,
            "parentId": null,
            "sort": null,
            "tagNamePathList": null
          }
        ],
        "ownerName": "string",
        "parentTagVoList": [
          {
            "tagName": null,
            "highlightTag": null,
            "id": null,
            "tagCode": null,
            "tagType": null,
            "description": null,
            "levelPath": null,
            "level": null,
            "parentId": null,
            "sort": null,
            "tagNamePathList": null
          }
        ],
        "operationTime": "string",
        "spaceName": "string",
        "spaceId": 0,
        "customizeTag": [
          {
            "tagName": null,
            "highlightTag": null,
            "id": null,
            "tagCode": null,
            "tagType": null,
            "description": null,
            "levelPath": null,
            "level": null,
            "parentId": null,
            "sort": null,
            "tagNamePathList": null
          }
        ],
        "downloadType": "string"
      }
    ],
    "pageNum": 0,
    "pageSize": 0,
    "size": 0,
    "startRow": 0,
    "endRow": 0,
    "pages": 0,
    "prePage": 0,
    "nextPage": 0,
    "hasPreviousPage": true,
    "hasNextPage": true,
    "navigatePages": 0,
    "navigatePageNums": [
      0
    ],
    "navigateFirstPage": 0,
    "navigateLastPage": 0,
    "firstPage": true,
    "lastPage": true
  }
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|[EsPageInfoWikiDocumentVo](#schemaespageinfowikidocumentvo)|false|none||none|

<h2 id="tocS_WikiDocumentVo">WikiDocumentVo</h2>

<a id="schemawikidocumentvo"></a>
<a id="schema_WikiDocumentVo"></a>
<a id="tocSwikidocumentvo"></a>
<a id="tocswikidocumentvo"></a>

```json
{
  "id": 0,
  "ossId": 0,
  "url": "string",
  "type": "string",
  "title": "string",
  "highlightTitle": "string",
  "summary": "string",
  "highlightSummary": "string",
  "status": "string",
  "delFlag": "string",
  "isLike": true,
  "isCollect": true,
  "wikiTagVoList": [
    {
      "tagName": "string",
      "highlightTag": "string",
      "id": 0,
      "tagCode": "string",
      "tagType": "string",
      "description": "string",
      "levelPath": "string",
      "level": "string",
      "parentId": 0,
      "sort": 0,
      "tagNamePathList": [
        "string"
      ]
    }
  ],
  "ownerName": "string",
  "parentTagVoList": [
    {
      "tagName": "string",
      "highlightTag": "string",
      "id": 0,
      "tagCode": "string",
      "tagType": "string",
      "description": "string",
      "levelPath": "string",
      "level": "string",
      "parentId": 0,
      "sort": 0,
      "tagNamePathList": [
        "string"
      ]
    }
  ],
  "operationTime": "string",
  "spaceName": "string",
  "spaceId": 0,
  "customizeTag": [
    {
      "tagName": "string",
      "highlightTag": "string",
      "id": 0,
      "tagCode": "string",
      "tagType": "string",
      "description": "string",
      "levelPath": "string",
      "level": "string",
      "parentId": 0,
      "sort": 0,
      "tagNamePathList": [
        "string"
      ]
    }
  ],
  "downloadType": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|ossId|integer(int64)|false|none||文件id|
|url|string|false|none||附件URL|
|type|string|false|none||文章类型  1:文档 2:链接 9:其他|
|title|string|false|none||标题|
|highlightTitle|string|false|none||高亮标题|
|summary|string|false|none||摘要|
|highlightSummary|string|false|none||高亮摘要|
|status|string|false|none||文件状态（0 停用，1启用）|
|delFlag|string|false|none||删除标志（0代表存在 2代表删除）|
|isLike|boolean|false|none||是否点赞|
|isCollect|boolean|false|none||是否收藏|
|wikiTagVoList|[[WikiTagVo](#schemawikitagvo)]|false|none||标签|
|ownerName|string|false|none||文档所属者|
|parentTagVoList|[[WikiTagVo](#schemawikitagvo)]|false|none||父类标签集合|
|operationTime|string|false|none||操作时间|
|spaceName|string|false|none||空间名称|
|spaceId|integer(int64)|false|none||空间id|
|customizeTag|[[WikiTagVo](#schemawikitagvo)]|false|none||自定义标签集合|
|downloadType|string|false|none||下载类型：1禁止下载；2审批流下载；3直接下载|

<h2 id="tocS_WikiTagVo">WikiTagVo</h2>

<a id="schemawikitagvo"></a>
<a id="schema_WikiTagVo"></a>
<a id="tocSwikitagvo"></a>
<a id="tocswikitagvo"></a>

```json
{
  "tagName": "string",
  "highlightTag": "string",
  "id": 0,
  "tagCode": "string",
  "tagType": "string",
  "description": "string",
  "levelPath": "string",
  "level": "string",
  "parentId": 0,
  "sort": 0,
  "tagNamePathList": [
    "string"
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|tagName|string|false|none||标签名称|
|highlightTag|string|false|none||标签高亮|
|id|integer(int64)|false|none||id|
|tagCode|string|false|none||标签编码|
|tagType|string|false|none||标签类型|
|description|string|false|none||标签描述|
|levelPath|string|false|none||层级路径|
|level|string|false|none||层级|
|parentId|integer(int64)|false|none||父id|
|sort|integer(int32)|false|none||排序|
|tagNamePathList|[string]|false|none||标签名称路径|

<h2 id="tocS_RListWikiTagVo">RListWikiTagVo</h2>

<a id="schemarlistwikitagvo"></a>
<a id="schema_RListWikiTagVo"></a>
<a id="tocSrlistwikitagvo"></a>
<a id="tocsrlistwikitagvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "tagName": "string",
      "highlightTag": "string",
      "id": 0,
      "tagCode": "string",
      "tagType": "string",
      "description": "string",
      "levelPath": "string",
      "level": "string",
      "parentId": 0,
      "sort": 0,
      "tagNamePathList": [
        "string"
      ]
    }
  ]
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|[[WikiTagVo](#schemawikitagvo)]|false|none||数据对象|

<h2 id="tocS_WikiDocFavouriteVo">WikiDocFavouriteVo</h2>

<a id="schemawikidocfavouritevo"></a>
<a id="schema_WikiDocFavouriteVo"></a>
<a id="tocSwikidocfavouritevo"></a>
<a id="tocswikidocfavouritevo"></a>

```json
{
  "id": 0,
  "docId": 0,
  "title": "string",
  "summary": "string",
  "wikiTagVoList": [
    {
      "tagName": "string",
      "highlightTag": "string",
      "id": 0,
      "tagCode": "string",
      "tagType": "string",
      "description": "string",
      "levelPath": "string",
      "level": "string",
      "parentId": 0,
      "sort": 0,
      "tagNamePathList": [
        "string"
      ]
    }
  ],
  "createTime": "2019-08-24T14:15:22Z",
  "parentTagVoList": [
    {
      "tagName": "string",
      "highlightTag": "string",
      "id": 0,
      "tagCode": "string",
      "tagType": "string",
      "description": "string",
      "levelPath": "string",
      "level": "string",
      "parentId": 0,
      "sort": 0,
      "tagNamePathList": [
        "string"
      ]
    }
  ],
  "customizeTag": [
    {
      "tagName": "string",
      "highlightTag": "string",
      "id": 0,
      "tagCode": "string",
      "tagType": "string",
      "description": "string",
      "levelPath": "string",
      "level": "string",
      "parentId": 0,
      "sort": 0,
      "tagNamePathList": [
        "string"
      ]
    }
  ],
  "downloadType": "string"
}

```

文档_收藏

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|docId|integer(int64)|false|none||文章id|
|title|string|false|none||none|
|summary|string|false|none||none|
|wikiTagVoList|[[WikiTagVo](#schemawikitagvo)]|false|none||标签|
|createTime|string(date-time)|false|none||创建时间|
|parentTagVoList|[[WikiTagVo](#schemawikitagvo)]|false|none||父类标签集合|
|customizeTag|[[WikiTagVo](#schemawikitagvo)]|false|none||自定义标签集合|
|downloadType|string|false|none||下载类型：1禁止下载；2审批流下载；3直接下载|

<h2 id="tocS_RWikiDocumentVo">RWikiDocumentVo</h2>

<a id="schemarwikidocumentvo"></a>
<a id="schema_RWikiDocumentVo"></a>
<a id="tocSrwikidocumentvo"></a>
<a id="tocsrwikidocumentvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "id": 0,
    "ossId": 0,
    "url": "string",
    "type": "string",
    "title": "string",
    "highlightTitle": "string",
    "summary": "string",
    "highlightSummary": "string",
    "status": "string",
    "delFlag": "string",
    "isLike": true,
    "isCollect": true,
    "wikiTagVoList": [
      {
        "tagName": "string",
        "highlightTag": "string",
        "id": 0,
        "tagCode": "string",
        "tagType": "string",
        "description": "string",
        "levelPath": "string",
        "level": "string",
        "parentId": 0,
        "sort": 0,
        "tagNamePathList": [
          "string"
        ]
      }
    ],
    "ownerName": "string",
    "parentTagVoList": [
      {
        "tagName": "string",
        "highlightTag": "string",
        "id": 0,
        "tagCode": "string",
        "tagType": "string",
        "description": "string",
        "levelPath": "string",
        "level": "string",
        "parentId": 0,
        "sort": 0,
        "tagNamePathList": [
          "string"
        ]
      }
    ],
    "operationTime": "string",
    "spaceName": "string",
    "spaceId": 0,
    "customizeTag": [
      {
        "tagName": "string",
        "highlightTag": "string",
        "id": 0,
        "tagCode": "string",
        "tagType": "string",
        "description": "string",
        "levelPath": "string",
        "level": "string",
        "parentId": 0,
        "sort": 0,
        "tagNamePathList": [
          "string"
        ]
      }
    ],
    "downloadType": "string"
  }
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|[WikiDocumentVo](#schemawikidocumentvo)|false|none||none|

<h2 id="tocS_RTableDataInfoWikiHotDocVo">RTableDataInfoWikiHotDocVo</h2>

<a id="schemartabledatainfowikihotdocvo"></a>
<a id="schema_RTableDataInfoWikiHotDocVo"></a>
<a id="tocSrtabledatainfowikihotdocvo"></a>
<a id="tocsrtabledatainfowikihotdocvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "rows": [
      {
        "docId": 0,
        "title": "string",
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "code": 0,
    "msg": "string"
  }
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|[TableDataInfoWikiHotDocVo](#schematabledatainfowikihotdocvo)|false|none||表格分页数据对象|

<h2 id="tocS_TableDataInfoWikiHotDocVo">TableDataInfoWikiHotDocVo</h2>

<a id="schematabledatainfowikihotdocvo"></a>
<a id="schema_TableDataInfoWikiHotDocVo"></a>
<a id="tocStabledatainfowikihotdocvo"></a>
<a id="tocstabledatainfowikihotdocvo"></a>

```json
{
  "total": 0,
  "rows": [
    {
      "docId": 0,
      "title": "string",
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "code": 0,
  "msg": "string"
}

```

表格分页数据对象

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||总记录数|
|rows|[[WikiHotDocVo](#schemawikihotdocvo)]|false|none||列表数据|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|

<h2 id="tocS_WikiHotDocVo">WikiHotDocVo</h2>

<a id="schemawikihotdocvo"></a>
<a id="schema_WikiHotDocVo"></a>
<a id="tocSwikihotdocvo"></a>
<a id="tocswikihotdocvo"></a>

```json
{
  "docId": 0,
  "title": "string",
  "createTime": "2019-08-24T14:15:22Z"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|docId|integer(int64)|false|none||文章id|
|title|string|false|none||文章标题|
|createTime|string(date-time)|false|none||时间|

<h2 id="tocS_WikiSpaceBo">WikiSpaceBo</h2>

<a id="schemawikispacebo"></a>
<a id="schema_WikiSpaceBo"></a>
<a id="tocSwikispacebo"></a>
<a id="tocswikispacebo"></a>

```json
{
  "id": 0,
  "name": "string",
  "code": "string",
  "status": "string",
  "isAdmin": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|name|string|false|none||none|
|code|string|false|none||none|
|status|string|false|none||none|
|isAdmin|boolean|false|none||none|

<h2 id="tocS_WikiSpaceRoleInsertBo">WikiSpaceRoleInsertBo</h2>

<a id="schemawikispaceroleinsertbo"></a>
<a id="schema_WikiSpaceRoleInsertBo"></a>
<a id="tocSwikispaceroleinsertbo"></a>
<a id="tocswikispaceroleinsertbo"></a>

```json
{
  "spaceId": 0,
  "roleId": 0,
  "spaceRoleObjectList": [
    {
      "objectType": "string",
      "unionCode": "string"
    }
  ]
}

```

新增空间角色对象

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|spaceId|integer(int64)|false|none||空间id|
|roleId|integer(int64)|false|none||角色id|
|spaceRoleObjectList|[[spaceRoleObject](#schemaspaceroleobject)]|false|none||空间角色列表|

<h2 id="tocS_TableDataInfoWikiSpaceRoleObjectVo">TableDataInfoWikiSpaceRoleObjectVo</h2>

<a id="schematabledatainfowikispaceroleobjectvo"></a>
<a id="schema_TableDataInfoWikiSpaceRoleObjectVo"></a>
<a id="tocStabledatainfowikispaceroleobjectvo"></a>
<a id="tocstabledatainfowikispaceroleobjectvo"></a>

```json
{
  "total": 0,
  "rows": [
    {
      "id": 0,
      "spaceId": 0,
      "roleId": 0,
      "objectType": "string",
      "userId": 0,
      "userName": "string",
      "nickName": "string",
      "deptId": 0,
      "deptCode": "string",
      "deptName": "string"
    }
  ],
  "code": 0,
  "msg": "string"
}

```

表格分页数据对象

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||总记录数|
|rows|[[WikiSpaceRoleObjectVo](#schemawikispaceroleobjectvo)]|false|none||列表数据|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|

<h2 id="tocS_WikiSpaceRoleObjectVo">WikiSpaceRoleObjectVo</h2>

<a id="schemawikispaceroleobjectvo"></a>
<a id="schema_WikiSpaceRoleObjectVo"></a>
<a id="tocSwikispaceroleobjectvo"></a>
<a id="tocswikispaceroleobjectvo"></a>

```json
{
  "id": 0,
  "spaceId": 0,
  "roleId": 0,
  "objectType": "string",
  "userId": 0,
  "userName": "string",
  "nickName": "string",
  "deptId": 0,
  "deptCode": "string",
  "deptName": "string"
}

```

空间用户角色配置信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键id|
|spaceId|integer(int64)|false|none||空间id|
|roleId|integer(int64)|false|none||角色id|
|objectType|string|false|none||对象类型(0-部门，1-人)|
|userId|integer(int64)|false|none||用户id|
|userName|string|false|none||用户工号|
|nickName|string|false|none||用户名称|
|deptId|integer(int64)|false|none||部门id|
|deptCode|string|false|none||部门编码|
|deptName|string|false|none||部门名称|

<h2 id="tocS_RTableDataInfoWikiSpaceRoleConfigVo">RTableDataInfoWikiSpaceRoleConfigVo</h2>

<a id="schemartabledatainfowikispaceroleconfigvo"></a>
<a id="schema_RTableDataInfoWikiSpaceRoleConfigVo"></a>
<a id="tocSrtabledatainfowikispaceroleconfigvo"></a>
<a id="tocsrtabledatainfowikispaceroleconfigvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "rows": [
      {
        "id": 0,
        "roleName": "string",
        "roleCode": "string"
      }
    ],
    "code": 0,
    "msg": "string"
  }
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|[TableDataInfoWikiSpaceRoleConfigVo](#schematabledatainfowikispaceroleconfigvo)|false|none||表格分页数据对象|

<h2 id="tocS_TableDataInfoWikiSpaceRoleConfigVo">TableDataInfoWikiSpaceRoleConfigVo</h2>

<a id="schematabledatainfowikispaceroleconfigvo"></a>
<a id="schema_TableDataInfoWikiSpaceRoleConfigVo"></a>
<a id="tocStabledatainfowikispaceroleconfigvo"></a>
<a id="tocstabledatainfowikispaceroleconfigvo"></a>

```json
{
  "total": 0,
  "rows": [
    {
      "id": 0,
      "roleName": "string",
      "roleCode": "string"
    }
  ],
  "code": 0,
  "msg": "string"
}

```

表格分页数据对象

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||总记录数|
|rows|[[WikiSpaceRoleConfigVo](#schemawikispaceroleconfigvo)]|false|none||列表数据|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|

<h2 id="tocS_WikiSpaceRoleConfigVo">WikiSpaceRoleConfigVo</h2>

<a id="schemawikispaceroleconfigvo"></a>
<a id="schema_WikiSpaceRoleConfigVo"></a>
<a id="tocSwikispaceroleconfigvo"></a>
<a id="tocswikispaceroleconfigvo"></a>

```json
{
  "id": 0,
  "roleName": "string",
  "roleCode": "string"
}

```

空间角色配置

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||角色id|
|roleName|string|false|none||角色名称|
|roleCode|string|false|none||角色编码|

<h2 id="tocS_WikiDocumentStatusBo">WikiDocumentStatusBo</h2>

<a id="schemawikidocumentstatusbo"></a>
<a id="schema_WikiDocumentStatusBo"></a>
<a id="tocSwikidocumentstatusbo"></a>
<a id="tocswikidocumentstatusbo"></a>

```json
{
  "ids": [
    0
  ],
  "status": 0,
  "downloadType": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|ids|[integer]|false|none||文档id（0 停用，1启用）|
|status|integer(int32)|false|none||文档状态（0 停用，1启用）|
|downloadType|string|false|none||下载类型 1禁止下载；2审批流下载；3直接下载|

<h2 id="tocS_WikiDocumentEditDocBo">WikiDocumentEditDocBo</h2>

<a id="schemawikidocumenteditdocbo"></a>
<a id="schema_WikiDocumentEditDocBo"></a>
<a id="tocSwikidocumenteditdocbo"></a>
<a id="tocswikidocumenteditdocbo"></a>

```json
{
  "id": 0,
  "title": "string",
  "keyword": "string",
  "ossId": 0,
  "url": "string",
  "attName": "string",
  "summary": "string",
  "spaceId": "string",
  "tagBo": [
    {
      "tagId": 0,
      "tagName": "string",
      "tagCode": "string",
      "tagType": "string",
      "description": "string",
      "parentId": 0,
      "sort": 0
    }
  ],
  "ownerName": "string",
  "fsUrl": "string",
  "customizeTag": [
    {
      "tagId": 0,
      "tagName": "string",
      "tagCode": "string",
      "tagType": "string",
      "description": "string",
      "parentId": 0,
      "sort": 0
    }
  ],
  "type": "string",
  "downloadType": "string",
  "collaboratorList": [
    {
      "createBy": 0,
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": 0,
      "updateTime": "2019-08-24T14:15:22Z",
      "params": {
        "property1": {},
        "property2": {}
      },
      "tenantId": "string",
      "id": 0,
      "docId": 0,
      "collaboratorType": "string",
      "unionCode": "string",
      "permissionType": "string",
      "delFlag": "string"
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||不带id是新增，带文件id是编辑|
|title|string|false|none||文档标题|
|keyword|string|false|none||关键词|
|ossId|integer(int64)|false|none||ossId|
|url|string|false|none||文档地址|
|attName|string|false|none||附件名|
|summary|string|false|none||摘要|
|spaceId|string|false|none||空间ID|
|tagBo|[[WikiTagBo](#schemawikitagbo)]|false|none||标签信息|
|ownerName|string|false|none||文档所属者|
|fsUrl|string|false|none||飞书文件地址|
|customizeTag|[[WikiTagBo](#schemawikitagbo)]|false|none||自定义标签|
|type|string|false|none||文档类型 1:文档 2:链接 9:其他|
|downloadType|string|false|none||下载类型：1禁止下载；2审批流下载；3直接下载|
|collaboratorList|[[WikiDocCollaborator](#schemawikidoccollaborator)]|false|none||权限列表|

<h2 id="tocS_WikiTagBo">WikiTagBo</h2>

<a id="schemawikitagbo"></a>
<a id="schema_WikiTagBo"></a>
<a id="tocSwikitagbo"></a>
<a id="tocswikitagbo"></a>

```json
{
  "tagId": 0,
  "tagName": "string",
  "tagCode": "string",
  "tagType": "string",
  "description": "string",
  "parentId": 0,
  "sort": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|tagId|integer(int64)|false|none||标签id|
|tagName|string|false|none||标签名称|
|tagCode|string|false|none||标签编码|
|tagType|string|false|none||标签类型|
|description|string|false|none||描述|
|parentId|integer(int64)|false|none||父级id|
|sort|integer(int32)|false|none||排序值|

<h2 id="tocS_RWikiDocumentManageVo">RWikiDocumentManageVo</h2>

<a id="schemarwikidocumentmanagevo"></a>
<a id="schema_RWikiDocumentManageVo"></a>
<a id="tocSrwikidocumentmanagevo"></a>
<a id="tocsrwikidocumentmanagevo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "createBy": 0,
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": 0,
    "updateTime": "2019-08-24T14:15:22Z",
    "params": {
      "property1": {},
      "property2": {}
    },
    "id": 0,
    "title": "string",
    "type": "string",
    "keyword": "string",
    "ossId": 0,
    "url": "string",
    "attName": "string",
    "summary": "string",
    "status": 0,
    "unionId": 0,
    "version": "string",
    "spaceId": 0,
    "ownerName": "string",
    "createByName": "string",
    "tagIds": [
      0
    ],
    "tagNames": "string",
    "customizeTag": [
      {
        "tagName": "string",
        "highlightTag": "string",
        "id": 0,
        "tagCode": "string",
        "tagType": "string",
        "description": "string",
        "levelPath": "string",
        "level": "string",
        "parentId": 0,
        "sort": 0,
        "tagNamePathList": [
          "string"
        ]
      }
    ],
    "downloadType": "string",
    "collaboratorList": [
      {
        "docId": 0,
        "collaboratorType": "string",
        "userId": 0,
        "userName": "string",
        "nickName": "string",
        "deptId": 0,
        "deptCode": "string",
        "deptName": "string",
        "delFlag": "string"
      }
    ]
  }
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|[WikiDocumentManageVo](#schemawikidocumentmanagevo)|false|none||none|

<h2 id="tocS_WikiDocumentManageVo">WikiDocumentManageVo</h2>

<a id="schemawikidocumentmanagevo"></a>
<a id="schema_WikiDocumentManageVo"></a>
<a id="tocSwikidocumentmanagevo"></a>
<a id="tocswikidocumentmanagevo"></a>

```json
{
  "createBy": 0,
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": 0,
  "updateTime": "2019-08-24T14:15:22Z",
  "params": {
    "property1": {},
    "property2": {}
  },
  "id": 0,
  "title": "string",
  "type": "string",
  "keyword": "string",
  "ossId": 0,
  "url": "string",
  "attName": "string",
  "summary": "string",
  "status": 0,
  "unionId": 0,
  "version": "string",
  "spaceId": 0,
  "ownerName": "string",
  "createByName": "string",
  "tagIds": [
    0
  ],
  "tagNames": "string",
  "customizeTag": [
    {
      "tagName": "string",
      "highlightTag": "string",
      "id": 0,
      "tagCode": "string",
      "tagType": "string",
      "description": "string",
      "levelPath": "string",
      "level": "string",
      "parentId": 0,
      "sort": 0,
      "tagNamePathList": [
        "string"
      ]
    }
  ],
  "downloadType": "string",
  "collaboratorList": [
    {
      "docId": 0,
      "collaboratorType": "string",
      "userId": 0,
      "userName": "string",
      "nickName": "string",
      "deptId": 0,
      "deptCode": "string",
      "deptName": "string",
      "delFlag": "string"
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|integer(int64)|false|none||创建者|
|createTime|string(date-time)|false|none||上传时间|
|updateBy|integer(int64)|false|none||更新者|
|updateTime|string(date-time)|false|none||更新时间|
|params|object|false|none||请求参数|
|» **additionalProperties**|object|false|none||none|
|id|integer(int64)|false|none||none|
|title|string|false|none||文档标题|
|type|string|false|none||文档类型 1:文档 2:链接 9:其他|
|keyword|string|false|none||关键词|
|ossId|integer(int64)|false|none||ossId|
|url|string|false|none||文档地址|
|attName|string|false|none||附件名|
|summary|string|false|none||摘要|
|status|integer(int32)|false|none||文章状态（0 停用，1启用）|
|unionId|integer(int64)|false|none||知识ID（大数据预留）|
|version|string|false|none||版本号|
|spaceId|integer(int64)|false|none||空间ID|
|ownerName|string|false|none||所属人|
|createByName|string|false|none||创建人名称|
|tagIds|[integer]|false|none||文件绑定标签id(详情)|
|tagNames|string|false|none||文件绑定标签名称(用逗号分割，示例：标签1,标签2,标签3）|
|customizeTag|[[WikiTagVo](#schemawikitagvo)]|false|none||自定义标签|
|downloadType|string|false|none||下载类型：1禁止下载；2审批流下载；3直接下载|
|collaboratorList|[[DocumentCollaboratorVo](#schemadocumentcollaboratorvo)]|false|none||权限人员列表|

<h2 id="tocS_TableDataInfoWikiDocumentManageVo">TableDataInfoWikiDocumentManageVo</h2>

<a id="schematabledatainfowikidocumentmanagevo"></a>
<a id="schema_TableDataInfoWikiDocumentManageVo"></a>
<a id="tocStabledatainfowikidocumentmanagevo"></a>
<a id="tocstabledatainfowikidocumentmanagevo"></a>

```json
{
  "total": 0,
  "rows": [
    {
      "createBy": 0,
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": 0,
      "updateTime": "2019-08-24T14:15:22Z",
      "params": {
        "property1": {},
        "property2": {}
      },
      "id": 0,
      "title": "string",
      "type": "string",
      "keyword": "string",
      "ossId": 0,
      "url": "string",
      "attName": "string",
      "summary": "string",
      "status": 0,
      "unionId": 0,
      "version": "string",
      "spaceId": 0,
      "ownerName": "string",
      "createByName": "string",
      "tagIds": [
        0
      ],
      "tagNames": "string",
      "customizeTag": [
        {
          "tagName": "string",
          "highlightTag": "string",
          "id": 0,
          "tagCode": "string",
          "tagType": "string",
          "description": "string",
          "levelPath": "string",
          "level": "string",
          "parentId": 0,
          "sort": 0,
          "tagNamePathList": [
            "string"
          ]
        }
      ],
      "downloadType": "string",
      "collaboratorList": [
        {
          "docId": 0,
          "collaboratorType": "string",
          "userId": 0,
          "userName": "string",
          "nickName": "string",
          "deptId": 0,
          "deptCode": "string",
          "deptName": "string",
          "delFlag": "string"
        }
      ]
    }
  ],
  "code": 0,
  "msg": "string"
}

```

表格分页数据对象

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||总记录数|
|rows|[[WikiDocumentManageVo](#schemawikidocumentmanagevo)]|false|none||列表数据|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|

<h2 id="tocS_RTableDataInfoWikiDocViewRecordVo">RTableDataInfoWikiDocViewRecordVo</h2>

<a id="schemartabledatainfowikidocviewrecordvo"></a>
<a id="schema_RTableDataInfoWikiDocViewRecordVo"></a>
<a id="tocSrtabledatainfowikidocviewrecordvo"></a>
<a id="tocsrtabledatainfowikidocviewrecordvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "rows": [
      {
        "docId": 0,
        "title": "string",
        "wikiTagList": [
          {
            "tagName": null,
            "highlightTag": null,
            "id": null,
            "tagCode": null,
            "tagType": null,
            "description": null,
            "levelPath": null,
            "level": null,
            "parentId": null,
            "sort": null,
            "tagNamePathList": null
          }
        ],
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "code": 0,
    "msg": "string"
  }
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|[TableDataInfoWikiDocViewRecordVo](#schematabledatainfowikidocviewrecordvo)|false|none||表格分页数据对象|

<h2 id="tocS_TableDataInfoWikiDocViewRecordVo">TableDataInfoWikiDocViewRecordVo</h2>

<a id="schematabledatainfowikidocviewrecordvo"></a>
<a id="schema_TableDataInfoWikiDocViewRecordVo"></a>
<a id="tocStabledatainfowikidocviewrecordvo"></a>
<a id="tocstabledatainfowikidocviewrecordvo"></a>

```json
{
  "total": 0,
  "rows": [
    {
      "docId": 0,
      "title": "string",
      "wikiTagList": [
        {
          "tagName": "string",
          "highlightTag": "string",
          "id": 0,
          "tagCode": "string",
          "tagType": "string",
          "description": "string",
          "levelPath": "string",
          "level": "string",
          "parentId": 0,
          "sort": 0,
          "tagNamePathList": [
            "string"
          ]
        }
      ],
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "code": 0,
  "msg": "string"
}

```

表格分页数据对象

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||总记录数|
|rows|[[WikiDocViewRecordVo](#schemawikidocviewrecordvo)]|false|none||列表数据|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|

<h2 id="tocS_WikiDocViewRecordVo">WikiDocViewRecordVo</h2>

<a id="schemawikidocviewrecordvo"></a>
<a id="schema_WikiDocViewRecordVo"></a>
<a id="tocSwikidocviewrecordvo"></a>
<a id="tocswikidocviewrecordvo"></a>

```json
{
  "docId": 0,
  "title": "string",
  "wikiTagList": [
    {
      "tagName": "string",
      "highlightTag": "string",
      "id": 0,
      "tagCode": "string",
      "tagType": "string",
      "description": "string",
      "levelPath": "string",
      "level": "string",
      "parentId": 0,
      "sort": 0,
      "tagNamePathList": [
        "string"
      ]
    }
  ],
  "createTime": "2019-08-24T14:15:22Z"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|docId|integer(int64)|false|none||文档ID|
|title|string|false|none||文档名称|
|wikiTagList|[[WikiTagVo](#schemawikitagvo)]|false|none||标签|
|createTime|string(date-time)|false|none||创建时间|

<h2 id="tocS_WikiFavouriteBo">WikiFavouriteBo</h2>

<a id="schemawikifavouritebo"></a>
<a id="schema_WikiFavouriteBo"></a>
<a id="tocSwikifavouritebo"></a>
<a id="tocswikifavouritebo"></a>

```json
{
  "pageSize": 0,
  "pageNum": 0,
  "orderByColumn": "string",
  "isAsc": "string",
  "keyWord": "string",
  "tagIdList": [
    0
  ]
}

```

点赞收藏列表查询

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageSize|integer(int32)|false|none||分页大小|
|pageNum|integer(int32)|false|none||当前页数|
|orderByColumn|string|false|none||排序列|
|isAsc|string|false|none||排序的方向desc或者asc|
|keyWord|string|false|none||关键字|
|tagIdList|[integer]|false|none||筛选标签|

<h2 id="tocS_EsPageInfoWikiDocFavouriteVo">EsPageInfoWikiDocFavouriteVo</h2>

<a id="schemaespageinfowikidocfavouritevo"></a>
<a id="schema_EsPageInfoWikiDocFavouriteVo"></a>
<a id="tocSespageinfowikidocfavouritevo"></a>
<a id="tocsespageinfowikidocfavouritevo"></a>

```json
{
  "total": 0,
  "list": [
    {
      "id": 0,
      "docId": 0,
      "title": "string",
      "summary": "string",
      "wikiTagVoList": [
        {
          "tagName": "string",
          "highlightTag": "string",
          "id": 0,
          "tagCode": "string",
          "tagType": "string",
          "description": "string",
          "levelPath": "string",
          "level": "string",
          "parentId": 0,
          "sort": 0,
          "tagNamePathList": [
            "string"
          ]
        }
      ],
      "createTime": "2019-08-24T14:15:22Z",
      "parentTagVoList": [
        {
          "tagName": "string",
          "highlightTag": "string",
          "id": 0,
          "tagCode": "string",
          "tagType": "string",
          "description": "string",
          "levelPath": "string",
          "level": "string",
          "parentId": 0,
          "sort": 0,
          "tagNamePathList": [
            "string"
          ]
        }
      ],
      "customizeTag": [
        {
          "tagName": "string",
          "highlightTag": "string",
          "id": 0,
          "tagCode": "string",
          "tagType": "string",
          "description": "string",
          "levelPath": "string",
          "level": "string",
          "parentId": 0,
          "sort": 0,
          "tagNamePathList": [
            "string"
          ]
        }
      ],
      "downloadType": "string"
    }
  ],
  "pageNum": 0,
  "pageSize": 0,
  "size": 0,
  "startRow": 0,
  "endRow": 0,
  "pages": 0,
  "prePage": 0,
  "nextPage": 0,
  "hasPreviousPage": true,
  "hasNextPage": true,
  "navigatePages": 0,
  "navigatePageNums": [
    0
  ],
  "navigateFirstPage": 0,
  "navigateLastPage": 0,
  "firstPage": true,
  "lastPage": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|list|[[WikiDocFavouriteVo](#schemawikidocfavouritevo)]|false|none||[文档_收藏]|
|pageNum|integer(int32)|false|none||none|
|pageSize|integer(int32)|false|none||none|
|size|integer(int32)|false|none||none|
|startRow|integer(int32)|false|none||none|
|endRow|integer(int32)|false|none||none|
|pages|integer(int32)|false|none||none|
|prePage|integer(int32)|false|none||none|
|nextPage|integer(int32)|false|none||none|
|hasPreviousPage|boolean|false|none||none|
|hasNextPage|boolean|false|none||none|
|navigatePages|integer(int32)|false|none||none|
|navigatePageNums|[integer]|false|none||none|
|navigateFirstPage|integer(int32)|false|none||none|
|navigateLastPage|integer(int32)|false|none||none|
|firstPage|boolean|false|none||none|
|lastPage|boolean|false|none||none|

<h2 id="tocS_RWikiFavouriteSizeVo">RWikiFavouriteSizeVo</h2>

<a id="schemarwikifavouritesizevo"></a>
<a id="schema_RWikiFavouriteSizeVo"></a>
<a id="tocSrwikifavouritesizevo"></a>
<a id="tocsrwikifavouritesizevo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "likeSize": 0,
    "collectSize": 0
  }
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|[WikiFavouriteSizeVo](#schemawikifavouritesizevo)|false|none||none|

<h2 id="tocS_WikiFavouriteSizeVo">WikiFavouriteSizeVo</h2>

<a id="schemawikifavouritesizevo"></a>
<a id="schema_WikiFavouriteSizeVo"></a>
<a id="tocSwikifavouritesizevo"></a>
<a id="tocswikifavouritesizevo"></a>

```json
{
  "likeSize": 0,
  "collectSize": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|likeSize|integer(int32)|false|none||none|
|collectSize|integer(int32)|false|none||none|

<h2 id="tocS_spaceRoleObject">spaceRoleObject</h2>

<a id="schemaspaceroleobject"></a>
<a id="schema_spaceRoleObject"></a>
<a id="tocSspaceroleobject"></a>
<a id="tocsspaceroleobject"></a>

```json
{
  "objectType": "string",
  "unionCode": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|objectType|string|false|none||对象类型 0:部门 1:用户|
|unionCode|string|false|none||对象编码  objectType=0时为部门编码，objectType=1时为用户编码|

<h2 id="tocS_REsPageInfoWikiDocFavouriteVo">REsPageInfoWikiDocFavouriteVo</h2>

<a id="schemarespageinfowikidocfavouritevo"></a>
<a id="schema_REsPageInfoWikiDocFavouriteVo"></a>
<a id="tocSrespageinfowikidocfavouritevo"></a>
<a id="tocsrespageinfowikidocfavouritevo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "list": [
      {
        "id": 0,
        "docId": 0,
        "title": "string",
        "summary": "string",
        "wikiTagVoList": [
          {
            "tagName": null,
            "highlightTag": null,
            "id": null,
            "tagCode": null,
            "tagType": null,
            "description": null,
            "levelPath": null,
            "level": null,
            "parentId": null,
            "sort": null,
            "tagNamePathList": null
          }
        ],
        "createTime": "2019-08-24T14:15:22Z",
        "parentTagVoList": [
          {
            "tagName": null,
            "highlightTag": null,
            "id": null,
            "tagCode": null,
            "tagType": null,
            "description": null,
            "levelPath": null,
            "level": null,
            "parentId": null,
            "sort": null,
            "tagNamePathList": null
          }
        ],
        "customizeTag": [
          {
            "tagName": null,
            "highlightTag": null,
            "id": null,
            "tagCode": null,
            "tagType": null,
            "description": null,
            "levelPath": null,
            "level": null,
            "parentId": null,
            "sort": null,
            "tagNamePathList": null
          }
        ],
        "downloadType": "string"
      }
    ],
    "pageNum": 0,
    "pageSize": 0,
    "size": 0,
    "startRow": 0,
    "endRow": 0,
    "pages": 0,
    "prePage": 0,
    "nextPage": 0,
    "hasPreviousPage": true,
    "hasNextPage": true,
    "navigatePages": 0,
    "navigatePageNums": [
      0
    ],
    "navigateFirstPage": 0,
    "navigateLastPage": 0,
    "firstPage": true,
    "lastPage": true
  }
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|[EsPageInfoWikiDocFavouriteVo](#schemaespageinfowikidocfavouritevo)|false|none||none|

<h2 id="tocS_RListTagWikiCountVo">RListTagWikiCountVo</h2>

<a id="schemarlisttagwikicountvo"></a>
<a id="schema_RListTagWikiCountVo"></a>
<a id="tocSrlisttagwikicountvo"></a>
<a id="tocsrlisttagwikicountvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "tagId": 0,
      "count": 0
    }
  ]
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|[[TagWikiCountVo](#schematagwikicountvo)]|false|none||数据对象|

<h2 id="tocS_TagWikiCountVo">TagWikiCountVo</h2>

<a id="schematagwikicountvo"></a>
<a id="schema_TagWikiCountVo"></a>
<a id="tocStagwikicountvo"></a>
<a id="tocstagwikicountvo"></a>

```json
{
  "tagId": 0,
  "count": 0
}

```

标签统计文章数

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|tagId|integer(int64)|false|none||标签id|
|count|integer(int64)|false|none||文章数|

<h2 id="tocS_RTableDataInfoSysUserBo">RTableDataInfoSysUserBo</h2>

<a id="schemartabledatainfosysuserbo"></a>
<a id="schema_RTableDataInfoSysUserBo"></a>
<a id="tocSrtabledatainfosysuserbo"></a>
<a id="tocsrtabledatainfosysuserbo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "rows": [
      {
        "userId": 0,
        "userName": "string",
        "nickName": "string",
        "phoneNumber": "string",
        "sex": "string",
        "deptName": "string"
      }
    ],
    "code": 0,
    "msg": "string"
  }
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|[TableDataInfoSysUserBo](#schematabledatainfosysuserbo)|false|none||表格分页数据对象|

<h2 id="tocS_RTableDataInfoWikiSpaceRoleObjectVo">RTableDataInfoWikiSpaceRoleObjectVo</h2>

<a id="schemartabledatainfowikispaceroleobjectvo"></a>
<a id="schema_RTableDataInfoWikiSpaceRoleObjectVo"></a>
<a id="tocSrtabledatainfowikispaceroleobjectvo"></a>
<a id="tocsrtabledatainfowikispaceroleobjectvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "rows": [
      {
        "id": 0,
        "spaceId": 0,
        "roleId": 0,
        "objectType": "string",
        "userId": 0,
        "userName": "string",
        "nickName": "string",
        "deptId": 0,
        "deptCode": "string",
        "deptName": "string"
      }
    ],
    "code": 0,
    "msg": "string"
  }
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|[TableDataInfoWikiSpaceRoleObjectVo](#schematabledatainfowikispaceroleobjectvo)|false|none||表格分页数据对象|

<h2 id="tocS_RListUserRoleVo">RListUserRoleVo</h2>

<a id="schemarlistuserrolevo"></a>
<a id="schema_RListUserRoleVo"></a>
<a id="tocSrlistuserrolevo"></a>
<a id="tocsrlistuserrolevo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "spaceId": "string",
      "spaceName": "string",
      "spaceCode": "string",
      "spaceStatus": "string",
      "roleList": [
        {
          "roleId": "string",
          "roleCode": "string",
          "roleName": "string"
        }
      ],
      "userTotal": 0,
      "isVisible": true
    }
  ]
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|[[UserRoleVo](#schemauserrolevo)]|false|none||数据对象|

<h2 id="tocS_Role">Role</h2>

<a id="schemarole"></a>
<a id="schema_Role"></a>
<a id="tocSrole"></a>
<a id="tocsrole"></a>

```json
{
  "roleId": "string",
  "roleCode": "string",
  "roleName": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|roleId|string|false|none||角色id|
|roleCode|string|false|none||角色编码|
|roleName|string|false|none||角色名称|

<h2 id="tocS_UserRoleVo">UserRoleVo</h2>

<a id="schemauserrolevo"></a>
<a id="schema_UserRoleVo"></a>
<a id="tocSuserrolevo"></a>
<a id="tocsuserrolevo"></a>

```json
{
  "spaceId": "string",
  "spaceName": "string",
  "spaceCode": "string",
  "spaceStatus": "string",
  "roleList": [
    {
      "roleId": "string",
      "roleCode": "string",
      "roleName": "string"
    }
  ],
  "userTotal": 0,
  "isVisible": true
}

```

用户空间角色

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|spaceId|string|false|none||空间id|
|spaceName|string|false|none||空间名称|
|spaceCode|string|false|none||空间编码|
|spaceStatus|string|false|none||空间状态|
|roleList|[[Role](#schemarole)]|false|none||角色列表|
|userTotal|integer(int32)|false|none||用户总数|
|isVisible|boolean|false|none||是否可见， 默认不可见，只有管理员可见|

<h2 id="tocS_RListWikiDocumentVo">RListWikiDocumentVo</h2>

<a id="schemarlistwikidocumentvo"></a>
<a id="schema_RListWikiDocumentVo"></a>
<a id="tocSrlistwikidocumentvo"></a>
<a id="tocsrlistwikidocumentvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "id": 0,
      "ossId": 0,
      "url": "string",
      "type": "string",
      "title": "string",
      "highlightTitle": "string",
      "summary": "string",
      "highlightSummary": "string",
      "status": "string",
      "delFlag": "string",
      "isLike": true,
      "isCollect": true,
      "wikiTagVoList": [
        {
          "tagName": "string",
          "highlightTag": "string",
          "id": 0,
          "tagCode": "string",
          "tagType": "string",
          "description": "string",
          "levelPath": "string",
          "level": "string",
          "parentId": 0,
          "sort": 0,
          "tagNamePathList": [
            "string"
          ]
        }
      ],
      "ownerName": "string",
      "parentTagVoList": [
        {
          "tagName": "string",
          "highlightTag": "string",
          "id": 0,
          "tagCode": "string",
          "tagType": "string",
          "description": "string",
          "levelPath": "string",
          "level": "string",
          "parentId": 0,
          "sort": 0,
          "tagNamePathList": [
            "string"
          ]
        }
      ],
      "operationTime": "string",
      "spaceName": "string",
      "spaceId": 0,
      "customizeTag": [
        {
          "tagName": "string",
          "highlightTag": "string",
          "id": 0,
          "tagCode": "string",
          "tagType": "string",
          "description": "string",
          "levelPath": "string",
          "level": "string",
          "parentId": 0,
          "sort": 0,
          "tagNamePathList": [
            "string"
          ]
        }
      ],
      "downloadType": "string"
    }
  ]
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|[[WikiDocumentVo](#schemawikidocumentvo)]|false|none||数据对象|

<h2 id="tocS_RListWikiSpecialVo">RListWikiSpecialVo</h2>

<a id="schemarlistwikispecialvo"></a>
<a id="schema_RListWikiSpecialVo"></a>
<a id="tocSrlistwikispecialvo"></a>
<a id="tocsrlistwikispecialvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "specialId": 0,
      "specialName": "string"
    }
  ]
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|[[WikiSpecialVo](#schemawikispecialvo)]|false|none||数据对象|

<h2 id="tocS_WikiSpecialVo">WikiSpecialVo</h2>

<a id="schemawikispecialvo"></a>
<a id="schema_WikiSpecialVo"></a>
<a id="tocSwikispecialvo"></a>
<a id="tocswikispecialvo"></a>

```json
{
  "specialId": 0,
  "specialName": "string"
}

```

专栏文章

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|specialId|integer(int64)|false|none||专栏id|
|specialName|string|false|none||专栏名称|

<h2 id="tocS_WikiSpecialBo">WikiSpecialBo</h2>

<a id="schemawikispecialbo"></a>
<a id="schema_WikiSpecialBo"></a>
<a id="tocSwikispecialbo"></a>
<a id="tocswikispecialbo"></a>

```json
{
  "pageSize": 0,
  "pageNum": 0,
  "orderByColumn": "string",
  "isAsc": "string",
  "specialId": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageSize|integer(int32)|false|none||分页大小|
|pageNum|integer(int32)|false|none||当前页数|
|orderByColumn|string|false|none||排序列|
|isAsc|string|false|none||排序的方向desc或者asc|
|specialId|integer(int64)|false|none||专栏id|

<h2 id="tocS_EsPageInfoObject">EsPageInfoObject</h2>

<a id="schemaespageinfoobject"></a>
<a id="schema_EsPageInfoObject"></a>
<a id="tocSespageinfoobject"></a>
<a id="tocsespageinfoobject"></a>

```json
{
  "total": 0,
  "list": [
    {}
  ],
  "pageNum": 0,
  "pageSize": 0,
  "size": 0,
  "startRow": 0,
  "endRow": 0,
  "pages": 0,
  "prePage": 0,
  "nextPage": 0,
  "hasPreviousPage": true,
  "hasNextPage": true,
  "navigatePages": 0,
  "navigatePageNums": [
    0
  ],
  "navigateFirstPage": 0,
  "navigateLastPage": 0,
  "firstPage": true,
  "lastPage": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|list|[object]|false|none||none|
|pageNum|integer(int32)|false|none||none|
|pageSize|integer(int32)|false|none||none|
|size|integer(int32)|false|none||none|
|startRow|integer(int32)|false|none||none|
|endRow|integer(int32)|false|none||none|
|pages|integer(int32)|false|none||none|
|prePage|integer(int32)|false|none||none|
|nextPage|integer(int32)|false|none||none|
|hasPreviousPage|boolean|false|none||none|
|hasNextPage|boolean|false|none||none|
|navigatePages|integer(int32)|false|none||none|
|navigatePageNums|[integer]|false|none||none|
|navigateFirstPage|integer(int32)|false|none||none|
|navigateLastPage|integer(int32)|false|none||none|
|firstPage|boolean|false|none||none|
|lastPage|boolean|false|none||none|

<h2 id="tocS_REsPageInfoObject">REsPageInfoObject</h2>

<a id="schemarespageinfoobject"></a>
<a id="schema_REsPageInfoObject"></a>
<a id="tocSrespageinfoobject"></a>
<a id="tocsrespageinfoobject"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "list": [
      {}
    ],
    "pageNum": 0,
    "pageSize": 0,
    "size": 0,
    "startRow": 0,
    "endRow": 0,
    "pages": 0,
    "prePage": 0,
    "nextPage": 0,
    "hasPreviousPage": true,
    "hasNextPage": true,
    "navigatePages": 0,
    "navigatePageNums": [
      0
    ],
    "navigateFirstPage": 0,
    "navigateLastPage": 0,
    "firstPage": true,
    "lastPage": true
  }
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|[EsPageInfoObject](#schemaespageinfoobject)|false|none||none|

<h2 id="tocS_OperateLogBo">OperateLogBo</h2>

<a id="schemaoperatelogbo"></a>
<a id="schema_OperateLogBo"></a>
<a id="tocSoperatelogbo"></a>
<a id="tocsoperatelogbo"></a>

```json
{
  "logType": "string",
  "content": "string",
  "systemTerminal": "string",
  "spaceName": "string",
  "roleName": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|logType|string|false|none||操作类型（新增：insert，修改：update，删除：delete，查看：view，分享：share）|
|content|string|false|none||日志内容|
|systemTerminal|string|false|none||系统终端|
|spaceName|string|false|none||空间名称|
|roleName|string|false|none||角色名|

<h2 id="tocS_OperateLogQueryBo">OperateLogQueryBo</h2>

<a id="schemaoperatelogquerybo"></a>
<a id="schema_OperateLogQueryBo"></a>
<a id="tocSoperatelogquerybo"></a>
<a id="tocsoperatelogquerybo"></a>

```json
{
  "content": "string",
  "empCode": "string",
  "logType": "string",
  "logTimeStart": "string",
  "logTimeEnd": "string",
  "systemTerminal": "string",
  "spaceName": "string",
  "roleName": "string",
  "empName": "string",
  "deptCode": "string",
  "deptName": "string",
  "pageSize": 0,
  "pageNum": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|content|string|false|none||日志内容|
|empCode|string|false|none||操作人工号|
|logType|string|false|none||操作类型（新增：insert，修改：update，删除：delete，查看：view，分享：share）|
|logTimeStart|string|false|none||操作时间（开始）|
|logTimeEnd|string|false|none||操作时间(结束)|
|systemTerminal|string|false|none||系统终端|
|spaceName|string|false|none||操作空间名称|
|roleName|string|false|none||角色名|
|empName|string|false|none||员工名称|
|deptCode|string|false|none||部门编号|
|deptName|string|false|none||部门名|
|pageSize|integer(int32)|false|none||none|
|pageNum|integer(int32)|false|none||none|

<h2 id="tocS_EsPageInfoWikiDocumentViewVo">EsPageInfoWikiDocumentViewVo</h2>

<a id="schemaespageinfowikidocumentviewvo"></a>
<a id="schema_EsPageInfoWikiDocumentViewVo"></a>
<a id="tocSespageinfowikidocumentviewvo"></a>
<a id="tocsespageinfowikidocumentviewvo"></a>

```json
{
  "total": 0,
  "list": [
    {
      "documentId": 0,
      "title": "string",
      "summary": "string",
      "status": "string",
      "wikiTagVoList": [
        {
          "tagName": "string",
          "highlightTag": "string",
          "id": 0,
          "tagCode": "string",
          "tagType": "string",
          "description": "string",
          "levelPath": "string",
          "level": "string",
          "parentId": 0,
          "sort": 0,
          "tagNamePathList": [
            "string"
          ]
        }
      ],
      "viewTime": "string",
      "traffic": 0,
      "customizeTag": [
        {
          "tagName": "string",
          "highlightTag": "string",
          "id": 0,
          "tagCode": "string",
          "tagType": "string",
          "description": "string",
          "levelPath": "string",
          "level": "string",
          "parentId": 0,
          "sort": 0,
          "tagNamePathList": [
            "string"
          ]
        }
      ],
      "coverUrl": "string"
    }
  ],
  "pageNum": 0,
  "pageSize": 0,
  "size": 0,
  "startRow": 0,
  "endRow": 0,
  "pages": 0,
  "prePage": 0,
  "nextPage": 0,
  "hasPreviousPage": true,
  "hasNextPage": true,
  "navigatePages": 0,
  "navigatePageNums": [
    0
  ],
  "navigateFirstPage": 0,
  "navigateLastPage": 0,
  "firstPage": true,
  "lastPage": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|list|[[WikiDocumentViewVo](#schemawikidocumentviewvo)]|false|none||[浏览记录]|
|pageNum|integer(int32)|false|none||none|
|pageSize|integer(int32)|false|none||none|
|size|integer(int32)|false|none||none|
|startRow|integer(int32)|false|none||none|
|endRow|integer(int32)|false|none||none|
|pages|integer(int32)|false|none||none|
|prePage|integer(int32)|false|none||none|
|nextPage|integer(int32)|false|none||none|
|hasPreviousPage|boolean|false|none||none|
|hasNextPage|boolean|false|none||none|
|navigatePages|integer(int32)|false|none||none|
|navigatePageNums|[integer]|false|none||none|
|navigateFirstPage|integer(int32)|false|none||none|
|navigateLastPage|integer(int32)|false|none||none|
|firstPage|boolean|false|none||none|
|lastPage|boolean|false|none||none|

<h2 id="tocS_REsPageInfoWikiDocumentViewVo">REsPageInfoWikiDocumentViewVo</h2>

<a id="schemarespageinfowikidocumentviewvo"></a>
<a id="schema_REsPageInfoWikiDocumentViewVo"></a>
<a id="tocSrespageinfowikidocumentviewvo"></a>
<a id="tocsrespageinfowikidocumentviewvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "list": [
      {
        "documentId": 0,
        "title": "string",
        "summary": "string",
        "status": "string",
        "wikiTagVoList": [
          {
            "tagName": null,
            "highlightTag": null,
            "id": null,
            "tagCode": null,
            "tagType": null,
            "description": null,
            "levelPath": null,
            "level": null,
            "parentId": null,
            "sort": null,
            "tagNamePathList": null
          }
        ],
        "viewTime": "string",
        "traffic": 0,
        "customizeTag": [
          {
            "tagName": null,
            "highlightTag": null,
            "id": null,
            "tagCode": null,
            "tagType": null,
            "description": null,
            "levelPath": null,
            "level": null,
            "parentId": null,
            "sort": null,
            "tagNamePathList": null
          }
        ],
        "coverUrl": "string"
      }
    ],
    "pageNum": 0,
    "pageSize": 0,
    "size": 0,
    "startRow": 0,
    "endRow": 0,
    "pages": 0,
    "prePage": 0,
    "nextPage": 0,
    "hasPreviousPage": true,
    "hasNextPage": true,
    "navigatePages": 0,
    "navigatePageNums": [
      0
    ],
    "navigateFirstPage": 0,
    "navigateLastPage": 0,
    "firstPage": true,
    "lastPage": true
  }
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|[EsPageInfoWikiDocumentViewVo](#schemaespageinfowikidocumentviewvo)|false|none||none|

<h2 id="tocS_WikiDocumentViewVo">WikiDocumentViewVo</h2>

<a id="schemawikidocumentviewvo"></a>
<a id="schema_WikiDocumentViewVo"></a>
<a id="tocSwikidocumentviewvo"></a>
<a id="tocswikidocumentviewvo"></a>

```json
{
  "documentId": 0,
  "title": "string",
  "summary": "string",
  "status": "string",
  "wikiTagVoList": [
    {
      "tagName": "string",
      "highlightTag": "string",
      "id": 0,
      "tagCode": "string",
      "tagType": "string",
      "description": "string",
      "levelPath": "string",
      "level": "string",
      "parentId": 0,
      "sort": 0,
      "tagNamePathList": [
        "string"
      ]
    }
  ],
  "viewTime": "string",
  "traffic": 0,
  "customizeTag": [
    {
      "tagName": "string",
      "highlightTag": "string",
      "id": 0,
      "tagCode": "string",
      "tagType": "string",
      "description": "string",
      "levelPath": "string",
      "level": "string",
      "parentId": 0,
      "sort": 0,
      "tagNamePathList": [
        "string"
      ]
    }
  ],
  "coverUrl": "string"
}

```

浏览记录

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|documentId|integer(int64)|false|none||文档ID|
|title|string|false|none||标题|
|summary|string|false|none||摘要|
|status|string|false|none||文件状态（0 停用，1启用）|
|wikiTagVoList|[[WikiTagVo](#schemawikitagvo)]|false|none||标签|
|viewTime|string|false|none||浏览时间|
|traffic|integer(int32)|false|none||浏览量|
|customizeTag|[[WikiTagVo](#schemawikitagvo)]|false|none||自定义标签集合|
|coverUrl|string|false|none||封面图地址（有效期一天）|

<h2 id="tocS_RemoteModelUser">RemoteModelUser</h2>

<a id="schemaremotemodeluser"></a>
<a id="schema_RemoteModelUser"></a>
<a id="tocSremotemodeluser"></a>
<a id="tocsremotemodeluser"></a>

```json
{
  "userId": 0,
  "empCode": "string",
  "empName": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|userId|integer(int64)|false|none||用户id|
|empCode|string|false|none||用户工号|
|empName|string|false|none||用户名称|

<h2 id="tocS_FastFileListBo">FastFileListBo</h2>

<a id="schemafastfilelistbo"></a>
<a id="schema_FastFileListBo"></a>
<a id="tocSfastfilelistbo"></a>
<a id="tocsfastfilelistbo"></a>

```json
{
  "parentId": "string",
  "searchKey": "string"
}

```

文件列表请求

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|parentId|string|false|none||父文件ID|
|searchKey|string|false|none||搜索关键词|

<h2 id="tocS_FastApiResponseListFastFileVo">FastApiResponseListFastFileVo</h2>

<a id="schemafastapiresponselistfastfilevo"></a>
<a id="schema_FastApiResponseListFastFileVo"></a>
<a id="tocSfastapiresponselistfastfilevo"></a>
<a id="tocsfastapiresponselistfastfilevo"></a>

```json
{
  "code": 0,
  "success": true,
  "message": "string",
  "data": [
    {
      "id": "string",
      "parentId": "string",
      "type": "string",
      "hasChild": true,
      "name": "string",
      "updateTime": "string",
      "createTime": "string"
    }
  ]
}

```

API通用响应

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||状态码|
|success|boolean|false|none||是否成功|
|message|string|false|none||消息|
|data|[[FastFileVo](#schemafastfilevo)]|false|none||数据|

<h2 id="tocS_FastFileVo">FastFileVo</h2>

<a id="schemafastfilevo"></a>
<a id="schema_FastFileVo"></a>
<a id="tocSfastfilevo"></a>
<a id="tocsfastfilevo"></a>

```json
{
  "id": "string",
  "parentId": "string",
  "type": "string",
  "hasChild": true,
  "name": "string",
  "updateTime": "string",
  "createTime": "string"
}

```

文件项响应

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none||文件ID|
|parentId|string|false|none||父文件ID|
|type|string|false|none||类型：file | folder|
|hasChild|boolean|false|none||是否有子文档|
|name|string|false|none||文件名称|
|updateTime|string|false|none||更新时间|
|createTime|string|false|none||创建时间|

<h2 id="tocS_FastApiResponseFastFileUrlVo">FastApiResponseFastFileUrlVo</h2>

<a id="schemafastapiresponsefastfileurlvo"></a>
<a id="schema_FastApiResponseFastFileUrlVo"></a>
<a id="tocSfastapiresponsefastfileurlvo"></a>
<a id="tocsfastapiresponsefastfileurlvo"></a>

```json
{
  "code": 0,
  "success": true,
  "message": "string",
  "data": {
    "url": "string"
  }
}

```

API通用响应

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||状态码|
|success|boolean|false|none||是否成功|
|message|string|false|none||消息|
|data|[FastFileUrlVo](#schemafastfileurlvo)|false|none||Fast文件URL响应VO|

<h2 id="tocS_FastFileUrlVo">FastFileUrlVo</h2>

<a id="schemafastfileurlvo"></a>
<a id="schema_FastFileUrlVo"></a>
<a id="tocSfastfileurlvo"></a>
<a id="tocsfastfileurlvo"></a>

```json
{
  "url": "string"
}

```

Fast文件URL响应VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|url|string|false|none||文件URL|

<h2 id="tocS_FastApiResponseFastFileVo">FastApiResponseFastFileVo</h2>

<a id="schemafastapiresponsefastfilevo"></a>
<a id="schema_FastApiResponseFastFileVo"></a>
<a id="tocSfastapiresponsefastfilevo"></a>
<a id="tocsfastapiresponsefastfilevo"></a>

```json
{
  "code": 0,
  "success": true,
  "message": "string",
  "data": {
    "id": "string",
    "parentId": "string",
    "type": "string",
    "hasChild": true,
    "name": "string",
    "updateTime": "string",
    "createTime": "string"
  }
}

```

API通用响应

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||状态码|
|success|boolean|false|none||是否成功|
|message|string|false|none||消息|
|data|[FastFileVo](#schemafastfilevo)|false|none||文件项响应|

<h2 id="tocS_FastApiResponseFastFileContentVo">FastApiResponseFastFileContentVo</h2>

<a id="schemafastapiresponsefastfilecontentvo"></a>
<a id="schema_FastApiResponseFastFileContentVo"></a>
<a id="tocSfastapiresponsefastfilecontentvo"></a>
<a id="tocsfastapiresponsefastfilecontentvo"></a>

```json
{
  "code": 0,
  "success": true,
  "message": "string",
  "data": {
    "title": "string",
    "content": "string",
    "previewUrl": "string"
  }
}

```

API通用响应

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||状态码|
|success|boolean|false|none||是否成功|
|message|string|false|none||消息|
|data|[FastFileContentVo](#schemafastfilecontentvo)|false|none||文件内容响应|

<h2 id="tocS_FastFileContentVo">FastFileContentVo</h2>

<a id="schemafastfilecontentvo"></a>
<a id="schema_FastFileContentVo"></a>
<a id="tocSfastfilecontentvo"></a>
<a id="tocsfastfilecontentvo"></a>

```json
{
  "title": "string",
  "content": "string",
  "previewUrl": "string"
}

```

文件内容响应

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|title|string|false|none||文档标题|
|content|string|false|none||文档内容|
|previewUrl|string|false|none||预览URL|

<h2 id="tocS_WikiDocCollaborator">WikiDocCollaborator</h2>

<a id="schemawikidoccollaborator"></a>
<a id="schema_WikiDocCollaborator"></a>
<a id="tocSwikidoccollaborator"></a>
<a id="tocswikidoccollaborator"></a>

```json
{
  "createBy": 0,
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": 0,
  "updateTime": "2019-08-24T14:15:22Z",
  "params": {
    "property1": {},
    "property2": {}
  },
  "tenantId": "string",
  "id": 0,
  "docId": 0,
  "collaboratorType": "string",
  "unionCode": "string",
  "permissionType": "string",
  "delFlag": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|integer(int64)|false|none||创建者|
|createTime|string(date-time)|false|none||创建时间|
|updateBy|integer(int64)|false|none||更新者|
|updateTime|string(date-time)|false|none||更新时间|
|params|object|false|none||请求参数|
|» **additionalProperties**|object|false|none||none|
|tenantId|string|false|none||租户编号|
|id|integer(int64)|false|none||none|
|docId|integer(int64)|false|none||文档ID|
|collaboratorType|string|false|none||协作者类型|
|unionCode|string|false|none||唯一标识（部门编码，工号）|
|permissionType|string|false|none||权限标识(阅读，分享，下载)|
|delFlag|string|false|none||删除标记|

<h2 id="tocS_WikiApplyBo">WikiApplyBo</h2>

<a id="schemawikiapplybo"></a>
<a id="schema_WikiApplyBo"></a>
<a id="tocSwikiapplybo"></a>
<a id="tocswikiapplybo"></a>

```json
{
  "applyType": "string",
  "applyTime": "2019-08-24T14:15:22Z",
  "applicant": "string",
  "applicantCode": "string",
  "postCode": "string",
  "postName": "string",
  "deptCode": "string",
  "deptName": "string",
  "ancestors": "string",
  "applyNodeInfo": {
    "property1": [
      {
        "userId": 0,
        "empCode": "string",
        "empName": "string"
      }
    ],
    "property2": [
      {
        "userId": 0,
        "empCode": "string",
        "empName": "string"
      }
    ]
  },
  "pcLink": "string",
  "appLink": "string",
  "processId": "string",
  "appCode": "string",
  "tenantId": "string",
  "docId": 0,
  "spaceId": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|applyType|string|false|none||审批类型|
|applyTime|string(date-time)|false|none||申请时间|
|applicant|string|false|none||申请人|
|applicantCode|string|false|none||申请人工号|
|postCode|string|false|none||岗位编码|
|postName|string|false|none||岗位名称|
|deptCode|string|false|none||部门编码|
|deptName|string|false|none||部门名称|
|ancestors|string|false|none||部门层级结构|
|applyNodeInfo|object|false|none||审批节点信息|
|» **additionalProperties**|[[RemoteModelUser](#schemaremotemodeluser)]|false|none||none|
|pcLink|string|false|none||pc跳转地址|
|appLink|string|false|none||app跳转地址|
|processId|string|false|none||流程定义id|
|appCode|string|false|none||app编码|
|tenantId|string|false|none||租户id|
|docId|integer(int64)|false|none||文档id|
|spaceId|integer(int64)|false|none||空间id|

<h2 id="tocS_RWikiDocumentApprovalStatusVo">RWikiDocumentApprovalStatusVo</h2>

<a id="schemarwikidocumentapprovalstatusvo"></a>
<a id="schema_RWikiDocumentApprovalStatusVo"></a>
<a id="tocSrwikidocumentapprovalstatusvo"></a>
<a id="tocsrwikidocumentapprovalstatusvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "documentId": 0,
    "hasApprovalFlow": true,
    "approvalStatus": "string"
  }
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|[WikiDocumentApprovalStatusVo](#schemawikidocumentapprovalstatusvo)|false|none||文档审批状态视图对象|

<h2 id="tocS_WikiDocumentApprovalStatusVo">WikiDocumentApprovalStatusVo</h2>

<a id="schemawikidocumentapprovalstatusvo"></a>
<a id="schema_WikiDocumentApprovalStatusVo"></a>
<a id="tocSwikidocumentapprovalstatusvo"></a>
<a id="tocswikidocumentapprovalstatusvo"></a>

```json
{
  "documentId": 0,
  "hasApprovalFlow": true,
  "approvalStatus": "string"
}

```

文档审批状态视图对象

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|documentId|integer(int64)|false|none||文档ID|
|hasApprovalFlow|boolean|false|none||是否有审批流数据|
|approvalStatus|string|false|none||审批状态<br /> 0，审批中 1，审批通过 2，审批拒绝 3，撤销审批 4，超时结束 5，强制终止|

<h2 id="tocS_DocumentCollaboratorVo">DocumentCollaboratorVo</h2>

<a id="schemadocumentcollaboratorvo"></a>
<a id="schema_DocumentCollaboratorVo"></a>
<a id="tocSdocumentcollaboratorvo"></a>
<a id="tocsdocumentcollaboratorvo"></a>

```json
{
  "docId": 0,
  "collaboratorType": "string",
  "userId": 0,
  "userName": "string",
  "nickName": "string",
  "deptId": 0,
  "deptCode": "string",
  "deptName": "string",
  "delFlag": "string"
}

```

文档权限

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|docId|integer(int64)|false|none||文档id|
|collaboratorType|string|false|none||协作者类型（0-部门，1-用户）|
|userId|integer(int64)|false|none||用户id|
|userName|string|false|none||用户工号|
|nickName|string|false|none||用户名称|
|deptId|integer(int64)|false|none||部门id|
|deptCode|string|false|none||部门编码|
|deptName|string|false|none||部门名称|
|delFlag|string|false|none||none|

