// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/vue-next/pull/3399

declare module 'vue' {
  export interface GlobalComponents {
    ButtonRepo: typeof import('./components/ButtonRepo.vue')['default']
    CopyRight: typeof import('./components/CopyRight.vue')['default']
    Empty: typeof import('./components/Empty.vue')['default']
    Footer: typeof import('./components/Footer.vue')['default']
    GithubRibbon: typeof import('./components/GithubRibbon.vue')['default']
    HelloWorld: typeof import('./components/HelloWorld.vue')['default']
    MyFooter: typeof import('./components/MyFooter.vue')['default']
    MyHeader: typeof import('./components/MyHeader.vue')['default']
    Pagination: typeof import('./components/Pagination.vue')['default']
    ParentTagList: typeof import('./components/ParentTagList.vue')['default']
    README: typeof import('./components/README.md')['default']
    Screen: typeof import('./components/Screen.vue')['default']
    ShareDialog: typeof import('./components/ShareDialog.vue')['default']
    SvgIcon: typeof import('./components/SvgIcon.vue')['default']
    TagList: typeof import('./components/TagList.vue')['default']
    TagListDemo: typeof import('./components/TagListDemo.vue')['default']
    TagListShort: typeof import('./components/TagListShort.vue')['default']
  }
}

export { }
