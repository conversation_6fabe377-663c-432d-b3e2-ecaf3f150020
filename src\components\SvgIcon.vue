<!-- 封装svg图标组件，用于管理颜色、宽高等样式。由于从figma导出的svg icon大小都是48x48，此处设置viewBox为48x48 -->
<template>
  <svg
    :width="width"
    :height="height"
    :viewBox="viewBox"
    xmlns="http://www.w3.org/2000/svg"
    :fill="color"
    v-bind="svgAttrs"
  >
    <slot />
  </svg>
</template>

<script>
export default {
  name: "SvgIcon",
  props: {
    color: {
      type: String,
      default: "currentColor", // 默认继承文本颜色
    },
    width: {
      type: [String, Number],
      default: 24,
    },
    height: {
      type: [String, Number],
      default: 24,
    },
    svgAttrs: {
      type: Object,
      default: () => ({}),
    },
    viewBox: {
      type: String,
      default: "0 0 48 48"
    }
  },
};
</script>
