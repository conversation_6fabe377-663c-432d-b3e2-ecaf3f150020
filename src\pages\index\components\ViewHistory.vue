<template>
  <div class="w-full rounded-[16px] p-[13px] bg-white min-h-[464px]">
    <div class="pb-[4px]">
      <img :src="titleIcon" class="min-w-[64px] h-[24px] mb-[6px]" />
<!--      <div class="text-[20px] font-500 text-[#17191F]">最近浏览</div>-->
    </div>
    <div v-if="list.length > 0" class="flex justify-between items-center cursor-pointer mt-[8px]" :key="item.docId"
      v-for="item in list" @click="handleToDetail(item.docId)">
      <div class="text-[#65676B] text-[14px] w-[190px] truncate">{{ item.title }}</div>
      <div class="text-[#C9CDD4] text-[14px]">
        {{ dayjs(item.createTime).format('MM/DD') }}
      </div>
    </div>
    <div v-if="list.length > 0"
      class="mt-[16px] text-center h-[32px] leading-[32px] bg-[#F2F3F5] text-[#65676B] text-[14px] cursor-pointer rounded-[6px]"
      @click="handleToHistory()">查看全部</div>
    <div v-else class="text-[#C9CDD4] text-[14px] mt-[25px] mb-[18px] flex flex-col justify-center items-center ">
      <SvgIcon :width="48" :height="48" class="mb-[8px]">
        <noDataSvg />
      </SvgIcon>
      暂无内容
    </div>
  </div>
</template>
<script setup>
import { onMounted, ref } from "vue";
import { useRouter, useRoute } from 'vue-router';
import titleIcon from '@/assets/images/title-view-history.png';
import dayjs from 'dayjs';
import { eventApi } from '@/api/wiki';
import { getHistoryDsl } from './dsl'
import SvgIcon from "@/components/SvgIcon.vue";
import noDataSvg from "@/assets/images/svgIcons/noData.vue";


const router = useRouter();

const list = ref([]);


const testGetEvent = async () => {
  const historyDsl = await getHistoryDsl()
  const res = await eventApi({ queryBody: JSON.stringify(historyDsl) })
  if (res.code === 200) {
    // 展示文章标题、点击次数
    const hotListData = JSON.parse(res.data).data[0].data_item_list.map(item => {
      return {
        title: item.event_params.Wiki_article_title,
        docId: item.event_params.Wiki_article_id,
        createTime: Math.floor(item.sum)
      }
    }).filter(item => !item.title.startsWith('{_rawValue')).slice(0, 12);  // 过滤掉title以{_rawValue开头的数据，取前4条
    list.value = hotListData
    console.log("最近浏览列表", hotListData)
  }
}

const handleToDetail = (docId) => {
  router.push({
    path: '/detail',
    query: {
      id: docId
    }
  })
}

const handleToHistory = () => {
  router.push({
    path: '/history'
  })
}

onMounted(() => {
  // getList();
  testGetEvent();
})
</script>
<style lang="scss" scoped></style>
