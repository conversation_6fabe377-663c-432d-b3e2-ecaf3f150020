<template>
  <div
    class="bg-[length:100%_200px] bg-no-repeat"
    :style="{ backgroundImage: `url(${JSZRRTBgImg})` }"
  >
    <div class="w-[1200px] mx-auto mb-[24px] pb-[18px]" style="min-height: calc(100vh - 209px)">
      <div class="breadcrumbs text-sm py-[17px]" style="padding-bottom: 0">
        <ul>
          <li class="text-white">首页</li>
          <li class="text-white">{{ route.query.specialName }}</li>
        </ul>
      </div>
      <div class="flex items-center justify-between">
        <img :src="JSZRRTImg" class="h-[41px]">
        <div class="relative">
          <img :src="dailyIcon" class="w-[141px] h-[141px]" />
          <div class="text-[#BEDAFF] text-[34px] font-bold absolute top-[50px] left-[50%] translate-x-[-50%]">{{ currentMonthName.substring(0, 3) }}</div>
          <div class="text-[#17191F] text-[34px] font-bold absolute top-[60px] left-[50%] left-[50%] translate-x-[-50%]">{{ currentDate }}</div>
        </div>
      </div>
      <div class="mt-[50px]">
        <div class="bg-white rounded-[8px] p-[17px] mb-[16px] cursor-pointer" v-for="(item, index) in list" :key="item.id" @click="e => handleToDetail(item, e)">
          <div class="flex justify-between pb-[5px] items-center">
            <div class="flex gap-[8px] w-[80%]">
              <div class="h-[24px] leading-[24px] px-[4px] bg-[#FFB537] text-center text-white text-[14px] rounded-[2px]">{{ index + 1 < 10 ? '0' + (index + 1) : index + 1 }}</div>
              <div class="text-[#17191F] text-[16px] font-bold">{{ item.title }}</div>
            </div>
            <div class="flex gap-[20px]">
              <img :src="downloadImg" class="w-[16px] h-[16px] cursor-pointer" @click="e => handleDownload(item, e)" v-if="spaceStore.canDownload(item.spaceName)" />
              <img :src="favIcon" v-if="!item.isCollect" class="w-[16px] h-[16px] cursor-pointer" @click="e => handleFav(item, e)" />
              <img :src="startedIcon" v-else class="w-[16px] h-[16px] cursor-pointer" @click="e => handleFav(item, e)" />
              <img :src="zhuanfaIcon" class="w-[16px] h-[16px] cursor-pointer" @click="e => handleShare(item, e)" />
            </div>
          </div>
          <div class="flex justify-between pt-[8px]">
            <TagList :data="[...(item.wikiTagVoList || []), ...(item.customizeTag || [])]" :showTagColor="true"/>
<!--            <div />-->
            <div class="flex gap-[16px]">
            <span class="text-[#B6B7BA] text-[12px] flex gap-[4px]" v-if="item.ownerName">
              <img :src="ownerNameImg" class="w-[18px] h-[18px]">
              {{ item.ownerName }}</span>
              <span class="text-[#B6B7BA] text-[12px] ">{{ dayjs(item.operationTime).format('YYYY-MM-DD') }}</span>
              <!--            <span class="text-[#B6B7BA] text-[12px] ">阅读1.2k</span>-->
            </div>
          </div>
        </div>
      </div>

      <div class="mb-[24px] flex justify-between">
        <div class="text-[14px]">
          共找到 <span class="text-[#FFB537]">{{ total }}</span> 条结果
        </div>
        <el-pagination
            v-model:current-page="pageNum"
            v-model:page-size="pageSize"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :total="total"
            layout="prev, pager, next, sizes, jumper"
        />
      </div>
    </div>
    <Footer />
    <ShareDialog ref="shareDialogRef" />
  </div>
</template>
<script setup>
import { computed, onMounted, ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import dayjs from 'dayjs';
import Footer from '@/components/Footer.vue';
import ShareDialog from '@/components/ShareDialog.vue';

import JSZRRTImg from '@/assets/images/jsz_rrt.png';
import JSZRRTBgImg from '@/assets/images/rrt_bg.png';
import dailyIcon from '@/assets/images/daily-icon.png';
import ownerNameImg from '@/assets/images/owner-name.png';
import zhuanfaIcon from '@/assets/images/zhuanfa_icon.png';
import downloadImg from '@/assets/images/download.png';
import favIcon from '@/assets/images/fav_icon.png';
import startedIcon from '@/assets/images/started.png';
import useSpaceStore from '@/stores/space';

const spaceStore = useSpaceStore();
import {modifyFavoriteApi, specialPageApi} from '@/api/wiki';
import { handleDownload } from '@/utils/download';

import { useCounterStore } from "@/stores/counter";
import {ElMessage} from "element-plus";
const counterStore = useCounterStore();
const router = useRouter();
const route = useRoute();

const count = computed(() => counterStore.count);
const currentMonthName = ref(dayjs().format('MMMM'));
const currentDate = ref(dayjs().date());
const pageSize = ref(10);
const pageNum = ref(1);
const total = ref(0);
const list = ref([]);
const shareDialogRef = ref(null);

const handleSizeChange = (val) => {
  console.log(`${val} items per page`)
  getList()
}
const handleCurrentChange = (val) => {
  console.log(`current page: ${val}`)
  getList()
}

const getList = async () => {
  try {
    const { code, data } = await specialPageApi({
      pageSize: pageSize.value,
      pageNum: pageNum.value,
      specialId: route.query.specialId,
    });
    if(code === 200) {
      list.value = data?.list;
      total.value = data.total;
    }
  } catch (e) {

  } finally {
  }
}

const handleToDetail = (row, e) => {
  e?.stopPropagation();
  router.push({
    path: '/detail',
    query: {
      id: row.id,
      ossId: row.ossId
    }
  })
}

const handleFav = async (row, e) => {
  e?.stopPropagation();
  // 1收藏 2点赞
  await modifyFavoriteApi({
    docId: row.id,
    type: 1
  });
  if(row.isCollect) {
    ElMessage.success('取消收藏！')
  }else {
    ElMessage.success('收藏成功！')
  };
  await getList();
}

const handleShare = async (row, e) => {
  e?.stopPropagation();
  shareDialogRef.value?.open(row);
}

onMounted(() => {
  getList()
});
</script>
<style scoped lang="scss">
.checked {
  color: #FFB537 !important;
}

.breadcrumbs > ul > li + *:before,
.breadcrumbs > ol > li + *:before {
  color: #fff !important;
}
</style>
