<template>
  <div class="">
    <div class="mb-[14px] mt-[36px]">
      <img :src="zhishichendian" class="w-[64px] h-[24px]" />
    </div>
    <div class="grid grid-cols-3 gap-3">
      <div class="flex gap-[15px] p-[16px] bg-white rounded-[16px] cursor-pointer" @click="handleToCate({ tagName: '产品知识'})">
        <div class="rounded-[8px] relative h-[125px] min-w-[86px]">
          <img class="w-full h-full" :src="chanpingzhishi" />
<!--          <div class="absolute bottom-0 left-0 h-[20px] px-[8px] bg-[#BAB6B0] text-white text-[12px] rounded-tr-[8px] rounded-bl-[8px]">23篇</div>-->
        </div>
        <div>
          <div class="flex justify-between pb-[8px] items-center">
            <div class="text-[#17191F] text-[16px] font-bold">
              产品知识
            </div>
            <div class="triangle"></div>
          </div>
          <div class="text-[12px] text-[#999]">
            提供全面的产品介绍和支持信息，包括产品详情、适应症、使用方法、剂型等
          </div>
        </div>
      </div>
      <div class="flex gap-[15px] p-[16px] bg-white rounded-[16px] cursor-pointer" @click="handleToCate({ tagName: '专家观念'})">
        <div class="rounded-[8px] relative h-[125px] min-w-[86px]">
          <img class="w-full h-full" :src="zhuanjiaguannian" />
<!--          <div class="absolute bottom-0 left-0 h-[20px] px-[8px] bg-[#BAB6B0] text-white text-[12px] rounded-tr-[8px] rounded-bl-[8px]">23篇</div>-->
        </div>
        <div>
          <div class="flex justify-between pb-[8px] items-center">
            <div class="text-[#17191F] text-[16px] font-bold">
              专家观念
            </div>
            <div class="triangle"></div>
          </div>
          <div class="text-[12px] text-[#999]">
            汇集疾病的诊断标准、治疗方案、疗效监测，并了解专家对常见治疗方法、面临的挑战以及药物使用的不同观念
          </div>
        </div>
      </div>
      <div class="flex gap-[15px] p-[16px] bg-white rounded-[16px] cursor-pointer" @click="handleToCate({ tagName: '科普内容'})">
        <div class="rounded-[8px] relative h-[125px] min-w-[86px]">
          <img class="w-full h-full" :src="kepuneirongImg" />
<!--          <div class="absolute bottom-0 left-0 h-[20px] px-[8px] bg-[#BAB6B0] text-white text-[12px] rounded-tr-[8px] rounded-bl-[8px]">23篇</div>-->
        </div>
        <div>
          <div class="flex justify-between pb-[8px] items-center">
            <div class="text-[#17191F] text-[16px] font-bold">
              科普内容
            </div>
            <div class="triangle"></div>
          </div>
          <div class="text-[12px] text-[#999]">
            科普宣传推广内容、疾病管理干预方案，以及科普活动所需的宣传物料，如宣传视频、三折页、课件等，支持科普活动的顺利开展
          </div>
        </div>
      </div>
      <div class="flex gap-[15px] p-[16px] bg-white rounded-[16px] cursor-pointer" @click="handleToCate({ tagName: '合规指引'})">
        <div class="rounded-[8px] relative h-[125px] min-w-[86px]">
          <img class="w-full h-full" :src="heguizhiyinImg" />
<!--          <div class="absolute bottom-0 left-0 h-[20px] px-[8px] bg-[#BAB6B0] text-white text-[12px] rounded-tr-[8px] rounded-bl-[8px]">23篇</div>-->
        </div>
        <div>
          <div class="flex justify-between pb-[8px] items-center">
            <div class="text-[#17191F] text-[16px] font-bold">
              合规指引
            </div>
            <div class="triangle"></div>
          </div>
          <div class="text-[12px] text-[#999]">
            汇集合规文件、操作指南以及各种合规模板，便于定期学习和遵守合规要求、法律法规
          </div>
        </div>
      </div>
      <div class="flex gap-[15px] p-[16px] bg-white rounded-[16px] cursor-pointer" @click="handleToCate({ tagName: '指南共识'})">
        <div class="rounded-[8px] relative h-[125px] min-w-[86px]">
          <img class="w-full h-full" :src="zhinangongshiImg" />
<!--          <div class="absolute bottom-0 left-0 h-[20px] px-[8px] bg-[#BAB6B0] text-white text-[12px] rounded-tr-[8px] rounded-bl-[8px]">23篇</div>-->
        </div>
        <div>
          <div class="flex justify-between pb-[8px] items-center">
            <div class="text-[#17191F] text-[16px] font-bold">
              指南共识
            </div>
            <div class="triangle"></div>
          </div>
          <div class="text-[12px] text-[#999]">
            收录医学领域的指南、共识和治疗方案，提供较为权威的医学参考，帮助理解跟进当前的治疗标准和未来趋势
          </div>
        </div>
      </div>
      <div class="flex gap-[15px] p-[16px] bg-white rounded-[16px] cursor-pointer" @click="handleToCate({ tagName: '研究文献'})">
        <div class="rounded-[8px] relative h-[125px] min-w-[86px]">
          <img class="w-full h-full" :src="yanjiuwenxianImg" />
<!--          <div class="absolute bottom-0 left-0 h-[20px] px-[8px] bg-[#BAB6B0] text-white text-[12px] rounded-tr-[8px] rounded-bl-[8px]">23篇</div>-->
        </div>
        <div>
          <div class="flex justify-between pb-[8px]">
            <div class="text-[#17191F] text-[16px] font-bold">
              研究文献
            </div>
            <div class="triangle"></div>
          </div>
          <div class="text-[12px] text-[#999]">
            提供文献情报、临床研究、最新资讯等具有科学依据的研究成果
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import chanpingzhishi from '@/assets/images/chanpingzhishi.png';
import zhuanjiaguannian from '@/assets/images/zhuanjiaguannian.png'
import zhishichendian from '@/assets/images/zhishichendian.png';

import kepuneirongImg from '@/assets/images/kepuneirong.png';
import heguizhiyinImg from '@/assets/images/heguizhiyin.png';
import zhinangongshiImg from '@/assets/images/zhinangongshi.png';
import yanjiuwenxianImg from '@/assets/images/yanjiuwenxian.png';
import { useRouter } from 'vue-router';

const router = useRouter();

const handleToCate = ({ tagName }) => {
  router.push({
    path: '/categoryDetail',
    query: {
      tagName: tagName || ''
    }
  })
}

</script>
<style scoped>
.triangle {
  width: 0;
  height: 0;
  border-bottom: 4px solid transparent;
  border-top: 4px solid transparent;
  border-left: 5px solid #C9CDD4;
}
</style>
