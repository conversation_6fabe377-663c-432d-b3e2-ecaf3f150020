# 文件列表及详情交互功能实现

## 功能概述

根据需求，实现了以下功能：

1. **文件类型判断和交互**

   - 文档类型 (type==="1")：保持原有交互，跳转到详情页
   - 链接类文件：点击文件名直接跳转至原链接
   - 其他类型文件：直接展示该文件本身

2. **下载按钮显示**

   - 默认展示下载按钮（移除了原有的权限判断条件）

3. **下载权限控制**
   - downloadType="1"：禁止下载，显示提示
   - downloadType="2"：审批流下载，需要检查审批状态
   - downloadType="3"：直接下载

## 实现的文件修改

### 1. API 接口 (`src/api/wiki.js`)

- 添加了 `getApprovalStatusApi` - 查询审批流状态
- 添加了 `downloadApplyApi` - 下载申请

### 2. 下载工具 (`src/utils/download.ts`)

- 重构了下载逻辑，添加了权限检查
- 实现了审批流程处理
- 添加了错误提示和用户反馈

### 3. 文件列表组件 (`src/pages/library/components/List.vue`)

- 修改了 `handleToDetail` 函数，根据文件类型进行不同处理
- 移除了下载按钮的权限限制，默认显示

### 4. 详情页面 (`src/pages/detail/index.vue`)

- 移除了下载按钮的权限限制，默认显示
- 添加了文件类型判断逻辑：
  - 当 `type==='2'` 时，只展示文件名称、大小和下载按钮（参考图片样式）
  - 当 `type==='1'` 或其他类型时，正常展示文件预览
- 右侧悬浮下载按钮在 `type==='2'` 时隐藏，避免重复

### 5. 专栏页面 (`src/pages/special/index.vue`)

- 修改了 `handleToDetail` 函数，根据文件类型进行不同处理
- 移除了下载按钮的权限限制，默认显示

### 6. 收藏页面 (`src/pages/favourite/index.vue`)

- 修改了 `handleToDetail` 函数，根据文件类型进行不同处理

### 7. 历史页面 (`src/pages/history/index.vue`)

- 修改了 `handleToDetail` 函数，根据文件类型进行不同处理

### 8. 首页新文章组件 (`src/pages/index/components/NewList.vue`)

- 修改了 `handleToDetail` 函数，根据文件类型进行不同处理

## 下载逻辑流程

### 审批流下载 (downloadType="2")

1. 调用 `/wiki/document/apply-status/{documentId}` 查询审批状态
2. 如果 `hasApprovalFlow` 为 false：
   - 调用 `/wiki/document/downLoad/apply` 提交下载申请
   - 显示"下载申请已提交，请等待审批"
3. 如果 `hasApprovalFlow` 为 true：
   - 检查 `approvalStatus` 字段：
     - "APPROVED"/"approved"：允许下载
     - "PENDING"/"pending"：显示"下载申请审批中，请耐心等待"
     - "REJECTED"/"rejected"：显示"下载申请已被拒绝"
     - 其他状态：显示"审批状态异常，请联系管理员"

### 文件类型处理逻辑

```javascript
const handleToDetail = (row, e) => {
  e?.stopPropagation();

  // 1. 文档类型，保持原有交互
  if (row.type === "1") {
    router.push({ path: "/detail", query: { id: row.id, ossId: row.ossId } });
    return;
  }

  // 2. 链接类文件，直接跳转
  if (
    row.url &&
    (row.url.startsWith("http://") || row.url.startsWith("https://"))
  ) {
    window.open(row.url, "_blank");
    return;
  }

  // 3. 其他类型文件，展示文件本身
  router.push({ path: "/detail", query: { id: row.id, ossId: row.ossId } });
};
```

## API 接口说明

### 查询审批流状态

```
GET /wiki/document/apply-status/{documentId}

Response:
{
  "code": 0,
  "msg": "string",
  "data": {
    "documentId": 0,
    "hasApprovalFlow": true,
    "approvalStatus": "string"
  }
}
```

### 下载申请

```
POST /wiki/document/downLoad/apply

Body:
{
  "docId": 0,
  "spaceId": 0,
  "applicant": "张浩威",
  "applicantCode": "GS5738",
  "postCode": "GSZN16F030",
  "postName": "Web工程师",
  "deptCode": "GSZN160203",
  "deptName": "后端开发部"
}

Response:
{
  "code": 0,
  "msg": "string",
  "data": "string"
}
```

#### 申请参数说明

- `docId`: 文档 ID
- `spaceId`: 空间 ID
- `applicant`: 申请人姓名（优先使用 nickName，fallback 到 userName）
- `applicantCode`: 申请人工号（userName）
- `postCode`: 岗位编码（取用户第一个岗位）
- `postName`: 岗位名称（取用户第一个岗位）
- `deptCode`: 部门编码
- `deptName`: 部门名称

## 详情页面展示逻辑

### 文件类型展示规则

1. **type==='1' (文档类型)**：

   - 正常展示文件预览（PDF、DOCX、PPTX、图片等）
   - 右侧悬浮按钮显示下载选项

2. **type==='2' (其他类型文件)**：
   - 主内容区显示文件信息卡片：
     - 文件图标（灰色文档图标）
     - 文件名称
     - 文件大小（格式化显示，如 13.50KB）
     - 下载按钮（蓝色按钮，带下载图标）
   - 右侧悬浮按钮不显示下载选项（避免重复）

### UI 样式说明

type==='2' 时的文件信息卡片样式：

- 居中显示，最大宽度 600px
- 白色背景，圆角边框
- 左侧：60x60px 灰色文档图标
- 中间：文件名（18px，粗体）+ 文件大小（14px，灰色）
- 右侧：蓝色下载按钮，带下载图标

## 测试

创建了测试文件 `src/utils/downloadTest.js` 用于验证功能逻辑。

## 注意事项

1. 所有下载操作都会记录操作日志
2. PDF 文件下载时会添加用户水印
3. 错误情况下会显示相应的用户提示
4. 链接类文件会在新窗口打开，避免影响当前页面
5. 部分组件（如 ViewHistory、Hot）的 handleToDetail 函数只接收 docId 参数，无法进行文件类型判断，保持原有跳转逻辑
6. 文件类型判断基于 row.type 字段和 row.url 字段
7. 下载权限检查基于 downloadType 字段：
   - "1": 禁止下载
   - "2": 审批流下载
   - "3": 直接下载

## 修改的文件列表

### 核心功能文件

- `src/api/wiki.js` - 添加 API 接口
- `src/utils/download.ts` - 重构下载逻辑

### 页面组件文件

- `src/pages/library/components/List.vue` - 文件列表组件
- `src/pages/detail/index.vue` - 详情页面
- `src/pages/special/index.vue` - 专栏页面
- `src/pages/favourite/index.vue` - 收藏页面
- `src/pages/history/index.vue` - 历史页面
- `src/pages/index/components/NewList.vue` - 首页新文章组件

### 测试文件

- `src/utils/downloadTest.js` - 功能测试文件
- `src/utils/detailPageTest.js` - 详情页面展示逻辑测试文件
