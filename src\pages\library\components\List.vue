<template>
  <div v-loading="loading">
    <div class="flex justify-between items-center mb-[16px] gap-[10px]">
      <div class="min-w-[96px] px-[16px] text-[--text-color-text-2] text-[14px] cursor-pointer flex items-center gap-[8px] justify-center bg-white py-[5px]" @click="handleSame()" :style="scoreAscStyle" v-if="showScore">
<!--        <SvgIcon v-if="orderByColumn === 'score'" :width="14" :height="14" viewBox="0 0 14 14">-->
<!--          <DescIcon />-->
<!--        </SvgIcon>-->
        <img v-if="orderByColumn === 'score'" :src="fullSortImg" class="w-[14px] h-[14px]">
        <SvgIcon v-else :width="14" :height="14" viewBox="0 0 14 14">
          <UnAscIcon />
        </SvgIcon>
        相关性
      </div>
      <div class="flex-1">
        <div class="text-[--text-color-text-2] text-[14px] cursor-pointer flex items-center gap-[8px] max-w-[110px] justify-center bg-white py-[5px]" @click="handleAsc()" :style="ascStyle">
          <SvgIcon :width="14" :height="14" viewBox="0 0 14 14">
            <AscIcon v-if="isAsc === 'asc'" />
            <DescIcon v-else-if="isAsc === 'desc'" />
            <UnAscIcon v-else />
          </SvgIcon>
          最新上传
        </div>
      </div>
      <div class="flex gap-[8px]">
        <el-input
            v-model="keyWord"
            class="w-[400px] h-[40px] leading-[40px]"
            placeholder="请输入标题、标签或摘要"
            :suffix-icon="Search"
            v-if="props.showSearch"
            @keyup.enter="getList()"
            maxlength="100"
        />
        <div v-else />
      </div>
    </div>
    <div>
      <div class="bg-white rounded-[8px] p-[17px] mb-[16px] cursor-pointer" v-for="(item, index) in list" :key="item.id" @click="e => handleToDetail(item, e)">
        <div class="flex justify-between pb-[5px] items-center">
          <div class="flex gap-[8px] w-[80%]">
            <div class="h-[24px] leading-[24px] px-[4px] bg-[#FFB537] text-center text-white text-[14px] rounded-[2px]">{{ index + 1 < 10 ? '0' + (index + 1) : index + 1 }}</div>
            <div class="text-[#17191F] text-[16px] font-bold" v-if="item.highlightTitle" v-html="item.highlightTitle" />
            <div class="text-[#17191F] text-[16px] font-bold" v-else>{{ item.title }}</div>
          </div>
          <div class="flex gap-[20px]">
            <img :src="downloadImg" class="w-[16px] h-[16px] cursor-pointer" @click="e => handleDownload(item, e)" v-if="spaceStore.canDownload(item.spaceName)" />
            <img :src="favIcon" v-if="!item.isCollect" class="w-[16px] h-[16px] cursor-pointer" @click="e => handleFav(item, e)" />
            <img :src="startedIcon" v-else class="w-[16px] h-[16px] cursor-pointer" @click="e => handleFav(item, e)" />
            <img :src="zhuanfaIcon" class="w-[16px] h-[16px] cursor-pointer" @click="e => handleShare(item, e)" />
          </div>
        </div>
        <div class="text-[--text-color-text-3] text-[14px]" v-if="item.summary">
          摘要：
          <span v-if="item.highlightSummary" v-html="item.highlightSummary" />
          <span v-else>{{ item.summary }}</span>
        </div>
        <div class="flex justify-between pt-[8px]">
          <TagList :data="[...(item.wikiTagVoList || []), ...(item.customizeTag || [])]" :showTagColor="true"/>
          <div class="flex gap-[16px]">
            <span class="text-[#B6B7BA] text-[12px] flex gap-[4px]" v-if="item.ownerName">
              <img :src="ownerNameImg" class="w-[18px] h-[18px]">
              {{ item.ownerName }}</span>
            <span class="text-[#B6B7BA] text-[12px] ">{{ dayjs(item.operationTime).format('YYYY-MM-DD') }}</span>
<!--            <span class="text-[#B6B7BA] text-[12px] ">阅读1.2k</span>-->
          </div>
        </div>
      </div>
    </div>

    <div class="mb-[24px] flex justify-between">
      <div class="text-[14px]">
        共找到 <span class="text-[#FFB537]">{{ total }}</span> 条结果
      </div>
      <el-pagination
        v-model:current-page="pageNum"
        v-model:page-size="pageSize"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :total="total"
        layout="prev, pager, next, sizes, jumper"
        size="small"
        :background="false"
      />
    </div>
  </div>
  <Empty v-if="total === 0" />

  <ShareDialog ref="shareDialogRef" />
</template>
<script setup>
import {onMounted, ref, watch, computed, onActivated} from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import dayjs from 'dayjs';
import { useRouter, useRoute } from 'vue-router';
import favIcon from '@/assets/images/fav_icon.png';
import startedIcon from '@/assets/images/started.png';
import Empty from '@/components/Empty.vue';
// import allResultIcon from '@/assets/images/all_result.png';
import { Search } from '@element-plus/icons-vue';
import TagList from '@/components/TagList.vue';
import { handleDownload } from '@/utils/download';
// import Pagination from '@/components/Pagination.vue';
import SvgIcon from "@/components/SvgIcon.vue";
import AscIcon from '@/assets/images/svgIcons/direction/asc.vue';
import DescIcon from '@/assets/images/svgIcons/direction/desc.vue';
import UnAscIcon from '@/assets/images/svgIcons/direction/unasc.vue';
import ownerNameImg from '@/assets/images/owner-name.png';
import zhuanfaIcon from '@/assets/images/zhuanfa_icon.png';
import ShareDialog from '@/components/ShareDialog.vue';
import fullSortImg from '@/assets/images/full-sort.png';
import downloadImg from '@/assets/images/download.png';
import useSpaceStore from '@/stores/space';

const spaceStore = useSpaceStore();

import {documentPageApi, modifyFavoriteApi, ossFileInfoApi} from '@/api/wiki';
import { useCounterStore } from '@/stores/counter';
const counterStore = useCounterStore()

const router = useRouter();
const route = useRoute();

const props =defineProps({
  keyWord: {
    type: String,
    default: ''
  },
  delay: {
    type: Boolean,
    default: false
  },
  showSearch: {
    type: Boolean,
    default: false
  },
  showIndex: {
    type: Boolean,
    default: false
  },
  spaceId: {
    type: String,
    default: ''
  },
  hideTotal: {
    type: Boolean,
    default: false
  },
  showScore: {
    type: Boolean,
    default: false
  }
})

const pages = ref([
  {
    label: '10条/页',
    value: 10
  },
  {
    label: '20条/页',
    value: 20
  },
  {
    label: '30条/页',
    value: 30
  },
  {
    label: '40条/页',
    value: 40
  },
  {
    label: '50条/页',
    value: 50
  },
]);

const pageSize = ref(route.query.pageSize && +route.query.pageSize || 10);
const pageNum = ref(route.query.pageNum && +route.query.pageNum || 1);
const total = ref(0);
const list = ref([]);
const loading = ref(false);
const keyWord = ref(route.query.keyWord || '');
const tagIdList = ref(route.query.tagIdList ? JSON.parse(route.query.tagIdList) : []);
const isAsc = ref(route.query.isAsc || undefined);
const orderByColumn = ref(route.query.orderByColumn || 'score');
const shareDialogRef = ref(null);
const showScore = ref(props.showScore);

const ascStyle = computed(() => {
  return {
    color: (isAsc.value && orderByColumn.value === 'operationTime') ? 'var(--primary-color)' : ''
  };
});

const scoreAscStyle = computed(() => {
  return {
    color: orderByColumn.value === 'score' ? 'var(--primary-color)' : ''
  };
});

const getList = async (query) => {
  try {
    loading.value = true;

    if(query) {
      pageNum.value = 1;
      tagIdList.value = query && query.tagIdList && query.tagIdList[0] === '-1' ? [] : (query.tagIdList && query.tagIdList.length ? query.tagIdList : ( route.query.tagIdList && JSON.parse(route.query.tagIdList)));
      // 表示没传，不是穿了个空字符串
      if(query.keyWord !== undefined) {
        keyWord.value = query?.keyWord;
      }
    }

    showScore.value = keyWord.value !== undefined && keyWord.value !== null && keyWord.value !== '';

    const { code, data } = await documentPageApi({
      pageSize: pageSize.value,
      pageNum: pageNum.value,
      keyWord: keyWord.value,
      tagIdList: tagIdList.value,
      spaceId: props.spaceId || route.query.spaceId,
      orderByColumn: orderByColumn.value,
      isAsc: isAsc.value
    });

    await router.replace(({
      path: window.location.pathname,
      query: {
        pageSize: pageSize.value,
        pageNum: pageNum.value,
        keyWord: keyWord.value,
        tagIdList: JSON.stringify(tagIdList.value),
        orderByColumn: orderByColumn.value,
        isAsc: isAsc.value,
        spaceId: props.spaceId,
        spaceName: route.query.spaceName,
        spaceCode: route.query.spaceCode
      }
    }))

    if(code === 200) {
      list.value = data?.list;
      total.value = data.total;
      counterStore.increment(data.total)
    }
  } catch (e) {

  } finally {
    loading.value = false;
  }
}

const pageChange = async () => {
  await getList();
}

const handleAsc = async () => {
  orderByColumn.value = 'operationTime'
  if(isAsc.value === 'asc') {
    isAsc.value = 'desc'
  }else {
    isAsc.value = 'asc'
  }

  await getList()
}

const handleSame = async () => {
  isAsc.value = undefined;
  orderByColumn.value = 'score'
  await getList()
}

const handleFav = async (row, e) => {
  e?.stopPropagation();
  // 1收藏 2点赞
  await modifyFavoriteApi({
    docId: row.id,
    type: 1
  });
  if(row.isCollect) {
    ElMessage.success('取消收藏！')
  }else {
    ElMessage.success('收藏成功！')
  };
  await getList();
}

const handleShare = async (row, e) => {
  e?.stopPropagation();
  shareDialogRef.value?.open(row);
}

const handleToDetail = (row, e) => {
  e?.stopPropagation();
  router.push({
    path: '/detail',
    query: {
      id: row.id,
      ossId: row.ossId
    }
  })
}

const handleSizeChange = (val) => {
  console.log(`${val} items per page`)
  getList()
}
const handleCurrentChange = (val) => {
  console.log(`current page: ${val}`)
  getList()
}

onMounted(() => {
  console.log('on mounted')
  getList()
})

defineExpose({
  getList
})

</script>
<style scoped>

::v-deep(input) {
  border: none;

}
::v-deep(input[type='text']:focus) {
  outline: none;
  box-shadow: none;
  border: none;
  font-size: 16px;
}

::v-deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: none;
  border: none;
}

::v-deep(.el-input__suffix-inner .el-icon) {
  color: #65676B;
  font-size: 20px;
}
</style>
<style>
em {
  color: #FFB537 !important;
  font-style: normal;
  font-weight: bold;
}
</style>
