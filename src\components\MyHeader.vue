<template>
  <header class="navbar bg-base-100 flex justify-between items-center">
    <div>
      <div class="flex-none">
<!--        <a class="btn btn-ghost text-xl normal-case">Gen Wiki</a>-->
        <router-link to="/"><img :src="logo" class="w-[124px] h-[28px] cursor-pointer ml-[36px]"></router-link>
      </div>
      <div class="flex-none">
        <ul class="menu-horizontal p-0 flax gap-[32px] ml-[30px] relative top-[5px]">
          <li class="text-[#4E595E] text-[14px] default-menu" :class="{'current-menu': currentPath === '/'}">
            <router-link to="/">首页</router-link>
          </li>
<!--          <li class="text-[#4E595E] text-[14px] default-menu" :class="{'text-[#4E595E]': true, 'text-[14px]': true, 'current-menu': currentPath === '/library'}">-->
<!--            <router-link to="/library">文件库</router-link>-->
<!--          </li>-->
          <li class="text-[#4E595E] text-[14px] default-menu" :class="{'text-[#4E595E]': true, 'text-[14px]': true, 'current-menu': currentPath === '/favourite'}">
            <router-link to="/favourite">我的收藏</router-link>
          </li>
<!--          <li>-->
<!--            <router-link to="/not-found">NotFound</router-link>-->
<!--          </li>-->
<!--          <li>-->
<!--            <router-link :to="`/hi/${counter.count}`" replace>DynamicRoute:{{ counter.count }}</router-link>-->
<!--          </li>-->
<!--          <li>-->
<!--            <router-link to="/about">About</router-link>-->
<!--          </li>-->
        </ul>
      </div>
      <div class="flex-1">
      </div>
    </div>
    <div class="flex items-center">
      <div class="text-[14px] text-gray-400 mr-[20px] cursor-pointer" @click="handleToOp">管理端入口</div>

      <div class="avatar-container">
        <el-dropdown @command="handleCommand" class="right-menu-item hover-effect" trigger="click">
          <div class="avatar-wrapper">
            <img :src="userStore.avatar" class="user-avatar" />
            <span class="name-avatar">{{userStore.nickname}}</span>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item divided command="logout">
                <span>退出登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

      </div>
    </div>
  </header>
</template>

<script setup>
import {computed, onMounted, ref, watch} from 'vue';
import { useRoute } from "vue-router";
import { ElMessageBox } from 'element-plus';
import logo from '@/assets/images/logo.png'
// import { useCounterStore } from '@/stores/counter';
import { useUserStore } from '@/stores/user';
import {getToken} from "../utils/auth";
const userStore = useUserStore();
// const counter = useCounterStore()
const route = useRoute();

const currentPath = ref('');

watch(
    () => route,
    (newRoute, oldRoute) => {
      console.log(newRoute, oldRoute)
      console.log(newRoute.path, 'newRoute.path')

      currentPath.value = newRoute.path;
    },
    {
      deep: true,
      immediate: true
    }
)

const handleToOp = () => {
  window.open(import.meta.env.VITE_APP_OP_URL, '__blank')
}

const logout = async () => {
  await ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    center: true,
    cancelButtonClass:'cancel-button-class'
  })
  await userStore.logout()
  location.href = '/login';
}

// 定义Command方法对象 通过key直接调用方法
const commandMap = {
  logout,
};
const handleCommand = (command) => {
  // 判断是否存在该方法
  if (commandMap[command]) {
    commandMap[command]();
  }
}

onMounted(() => {
  if(getToken()) {
    userStore.getInfo();
  }

})
</script>
<style scoped lang="scss">
.navbar {
  border-bottom: 1px solid #F4F5F7;
  padding: 0;
}

.default-menu {
  border-bottom: 2px solid #fff; padding-bottom: 10px
}
.current-menu {
  border-bottom: 2px solid #FFB537; color: #FFB537; padding-bottom: 10px
}
.avatar-container {
  margin-right: 40px;
  height: 100%;
  font-size: 16px;
  font-weight: 500;

  .avatar-wrapper {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    cursor: pointer;
    .user-avatar {
      cursor: pointer;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      margin-right: 16px;
    }
    .name-avatar {
      margin-right: 8px;
      font-size:16px;
    }

    i {
      cursor: pointer;
      // position: absolute;
      // right: -20px;
      // top: 25px;
      font-size: 12px;
    }
  }
}

</style>
