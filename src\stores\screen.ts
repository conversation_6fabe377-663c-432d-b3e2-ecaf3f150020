// @ts-nocheck
import {acceptHMRUpdate, defineStore} from "pinia";
import { ref } from 'vue';
import to from 'await-to-js';
import { spaceTagsApi } from '@/api/wiki';
import {useCounterStore} from "./counter";

export const useScreenStore = defineStore('screen', () => {
    const nodeList = ref<any>([]);

    const getNodeList = async (spaceId: string): Promise<void> => {
        const [err, res]: [any, any] = await to(spaceTagsApi(spaceId));
        if (res) {
            nodeList.value = res.data;
            return Promise.resolve();
        }
        return Promise.reject(err);
    };

    const resetNodeList = () => {
        nodeList.value = []
    }

    return {
        nodeList,
        getNodeList,
        resetNodeList
    };
}, {
    persist: true
})

if (import.meta.hot)
    import.meta.hot.accept(acceptHMRUpdate(useCounterStore, import.meta.hot));

export default useScreenStore;
