// Description: dsl配置文件
// 获取vite环境变量
const env = import.meta.env.MODE === 'production' ? 'production' : 'development'
const userName = window.localStorage.getItem('userName')
//  获取spaceId列表, 限制查询范围为当前用户可见的space
import { spaceListAPi } from '@/api/wiki.js';

export const getSpaceList = async () => {
  try {
    const res = await spaceListAPi({
      isAdmin: false
    });
    return res?.data || [];
    /** 返回数据格式示例
      [
        {
          "spaceId": "1868581073439002625",
          "spaceName": "金赛增专栏",
          "spaceCode": "CSKJ1",
          "spaceStatus": "1",
          "roleList": [
            {
              "roleId": "3",
              "roleCode": "file_manager",
              "roleName": "文档管理"
            }
          ]
        }
      ]
     */
  } catch (e) {
    console.error('获取空间列表失败:', e);
    return [];
  }
}
// 修改为异步函数
export const getHotDsl = async () => {
  const spaceIds = await getSpaceList().then(list => list.filter(d => d.isVisible).map(item => item.spaceId));

  return {

// export const hotDsl = {
  "version": 3,
  "app_ids": [10000060],
  "use_app_cloud_id": true,
  // 查询时间范围配置
  // "periods": [
  //   {
  //     "granularity": "day",
  //     "type": "last",
  //     "last": {
  //       "amount": 5,
  //       "unit": "day"
  //     },
  //     "timezone": "Asia/Shanghai"
  //   }
  // ],
  // 查询时间范围配置 2025-01-13 ~ 当前时间
  "periods": [
    {
      "granularity": "month",
      "type": "past_range",
      "spans": [
        {
          "type": "timestamp", "timestamp": "1736697600"
        }, {
          "type": "timestamp", "timestamp": Math.floor(Date.now() / 1000)
        }
      ], "timezone": "Asia/Shanghai", "week_start": 1
    }
  ],
  "content": {
    // 查询类型为事件分析
    "query_type": "event",
    // 分组条件，只支持公共属性
    "profile_groups_v2": [],
    // 排序条件
    orders: [
      // {
      //   "field": "profile#article_id",
      //   "order": "asc"
      // }
    ],
    // 过滤条件:公共属性 Wiki_env = development && 文章标题不为空 && 文章space属于指定的space
    "profile_filters": [
      {
        "show_name": "devOrProd",
        // "show_label": "1",
        "expression": {
          "logic": "and",
          "conditions": [
            {
              "property_type": "common_param",
              "property_name": "Wiki_env",
              "property_compose_type": "origin",
              "property_operation": "=",
              "property_values": [env]
            },
            {
              "property_type": "event_param",
              "property_name": "Wiki_article_title",
              "property_compose_type": "origin",
              "property_operation": "is_not_null",
              "property_values": []
            },
            {
              "property_type": "event_param",
              "property_name": "Wiki_article_space_id",
              "property_compose_type": "origin",
              "property_operation": "in",
              "property_values": [
                ...spaceIds
              ]
            }
          ]
        }
      }
    ],
    // 查询，二维数组
    "queries": [
      [
        {
          "event_type": "origin",
          "show_name": "文章id，文章标题",
          "event_name": "article_view_start",
          // 按 事件属性 文章标题、id 分组
          "groups_v2": [
            {
              "property_compose_type": "origin",
              "property_name": "Wiki_article_id",
              "property_type": "event_param"
            },
            {
              "property_compose_type": "origin",
              "property_name": "Wiki_article_title",
              "property_type": "event_param"
            },
          ],
          "filters": [],
          "show_label": "A",
          "event_indicator": "events"// 计算指标：总次数
        }
      ]
    ],
    // // 排序
    // "chartConfiguration": {
    //   "resultSortOrder": "sum_desc"
    // },
    // 分页设置: 每页10条
    "page": {
      "limit": 10,
      "offset": 0
    },
    "option": {
      refresh_cache: true,
      // "skip_cache": false,
      // "finder": { sort_rule: "sum_asc" }
    }
  }
}
}

export const getHistoryDsl = async () => {
  const spaceIds = await getSpaceList().then(list => list.map(item => item.spaceId));

  return {
// export const historyDsl = {
  "version": 3,
  "app_ids": [10000060],
  "use_app_cloud_id": true,
  // 查询时间范围配置
  "periods": [
    {
      "granularity": "all",
      "type": "past_range",
      "spans": [
        {
          "type": "timestamp", "timestamp": Math.floor(Date.now() / 1000) - 86400 * 7
        }, {
          "type": "timestamp", "timestamp": Math.floor(Date.now() / 1000)
        }
      ], "timezone": "Asia/Shanghai", "week_start": 1, align_unit: "day"
    }
  ],
  "content": {
    // 查询类型为事件分析
    "query_type": "event",
    // 分组条件，只支持公共属性
    "profile_groups_v2": [],
    // 排序条件
    // orders: [
    //   {
    //     "field": "event_param#Wiki_article_open_time",
    //     "order": "desc"
    //   }
    // ],
    // 过滤条件:公共属性 Wiki_env = development/production && 用户名 = userName && 文章标题不为空
    "profile_filters": [
      {
        "show_name": "devOrProd",
        // "show_label": "1",
        "expression": {
          "logic": "and",
          "conditions": [
            {
              "property_type": "common_param",
              "property_name": "Wiki_env",
              "property_compose_type": "origin",
              "property_operation": "=",
              "property_values": [env]
            },
            {
              "property_type": "event_param",
              "property_name": "Wiki_article_title",
              "property_compose_type": "origin",
              "property_operation": "is_not_null",
              "property_values": []
            },
            {
              "property_type": "user_profile",
              "property_name": "Wiki_Name",
              "property_compose_type": "origin",
              "property_operation": "=",
              "property_values": [
                userName
              ]
            },
            {
              "property_type": "event_param",
              "property_name": "Wiki_article_space_id",
              "property_compose_type": "origin",
              "property_operation": "in",
              "property_values": [
                ...spaceIds
              ]
            }
          ]
        }
      }
    ],
    // 查询，二维数组
    "query_type": "event",
    "queries": [
      [
        {
          "indicator_show_name": "按Wiki_article_open_time求最大值",
          "measure_info": {
            "measure_type": "max",
            "property_type": "event_param",
            "property_name": "Wiki_article_open_time",
            "property_compose_type": "origin"
          },
          "event_indicator": "measure",
          "show_name": "",
          "show_label": "A",
          "event_name": "article_view_start",
          "event_type": "origin",
          "filters": [],
          "groups_v2": [
            {
              "property_compose_type": "origin",
              "property_name": "Wiki_article_title",
              "property_type": "event_param"
            },
            {
              "property_compose_type": "origin",
              "property_name": "Wiki_article_id",
              "property_type": "event_param"
            },
          ],
          "extra": {}
        }
      ]
    ],
    // 分页设置: 每页20条
    "page": {
      "limit": 20,
      "offset": 0
    },
    "option": {
      // "skip_cache": true,
      "refresh_cache": true,
      // "finder": { sort_rule: "sum_asc" }
    }
  }
}
}
// 参考用 埋点平台接口使用的dsl
export const dsl = {
  "use_app_cloud_id": true,
  "periods": [
    {
      "granularity": "week",
      "type": "past_range",
      "spans": [
        {
          "type": "past",
          "past": {
            "amount": 0,
            "unit": "week"
          }
        },
        {
          "type": "past",
          "past": {
            "amount": 0,
            "unit": "week"
          }
        }
      ],
      "timezone": "Asia/Shanghai",
      "week_start": 1
    },
    {
      "granularity": "all",
      "type": "past_range",
      "spans": [
        {
          "type": "past",
          "past": {
            "amount": 0,
            "unit": "week"
          }
        },
        {
          "type": "past",
          "past": {
            "amount": 0,
            "unit": "week"
          }
        }
      ],
      "timezone": "Asia/Shanghai",
      "week_start": 1,
      "align_unit": "month",
      "skip_period": true
    }
  ],
  "version": 3,
  "content": {
    "profile_groups_v2": [],
    "profile_filters": [
      {
        "show_name": "development; GS8909; 不为空(Wiki_article_title)",
        "show_label": "1",
        "expression": {
          "logic": "and",
          "expressions": [
            {
              "logic": "or",
              "conditions": [
                {
                  "property_type": "common_param",
                  "property_name": "Wiki_env",
                  "property_compose_type": "origin",
                  "property_operation": "=",
                  "property_values": [
                    "development"
                  ]
                }
              ]
            },
            {
              "logic": "or",
              "conditions": [
                {
                  "property_type": "event_param",
                  "property_name": "Wiki_article_title",
                  "property_compose_type": "origin",
                  "property_operation": "is_not_null",
                  "property_values": []
                }
              ]
            },
            {
              "logic": "or",
              "conditions": [
                {
                  "property_type": "user_profile",
                  "property_name": "Wiki_Name",
                  "property_compose_type": "origin",
                  "property_operation": "=",
                  "property_values": [
                    userName
                  ]
                }
              ]
            }
          ]
        }
      }
    ],
    "orders": [],
    "query_type": "event",
    "queries": [
      [
        {
          "indicator_show_name": "按Wiki_article_open_time求最大值",
          "measure_info": {
            "measure_type": "max",
            "property_type": "event_param",
            "property_name": "Wiki_article_open_time",
            "property_compose_type": "origin"
          },
          "event_indicator": "measure",
          "show_name": "",
          "show_label": "A",
          "event_name": "article_view_start",
          "event_type": "origin",
          "filters": [],
          "groups_v2": [
            {
              "property_compose_type": "origin",
              "property_name": "Wiki_article_title",
              "property_type": "event_param"
            },
            {
              "property_compose_type": "origin",
              "property_name": "Wiki_article_id",
              "property_type": "event_param"
            }
          ],
          "extra": {}
        }
      ]
    ],
    "page": {
      "limit": 200,
      "offset": 0
    },
    "option": {
      "refresh_cache": true,
      "fusion": false,
      "insight": {},
      "analysis_subject": {},
      "accumulation": false,
      "skip_period_restrict": false
    }
  },
  "option": {
    "blend": {
      "status": true,
      "base": 0,
      "base_period": true
    },
    "transpose": false,
    "finder": {
      "sort_rule": "avg_desc"
    }
  },
  "app_ids": [
    10000060
  ]
}
