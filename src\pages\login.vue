<template>
  <div class="login">
    <div class="login-content w-[532px] h-[412px] rounded-[24px] py-[48px] px-[24px]">
      <img class="login-logo" :src="logo" alt="" />
      <div class="text-[#17191F] text-[28px] font-500 mt-[24px] h-[39px] leading-[39px]">
        自研创新型知识沉淀平台
      </div>
      <div class="text-[#65676B] text-[14px] mt-[4px] h-[32px] leading-[32px]">集成前沿 AI 模型，在信息安全和合规保障下，为用户构建开放协作的知识生态</div>
      <div class="feishu-btn mt-[108px] cursor-pointer" @click="handleLogin()" >
        <img class="login-icon" :src="iconBtn" alt="" />
        飞书用户一键登录
      </div>
    </div>
  </div>
</template>

<route lang="yaml">
meta:
  layout: login
</route>

<script setup>
import iconBtn from '@/assets/images/feishu.png';
import logo from '@/assets/images/logo.png';

const handleLogin = () => {
 window.location.href = import.meta.env.VITE_APP_FEISHU_AUTH_URL;
}
</script>

<style lang="scss" scoped>
.login {
  display: flex;
  height: 100vh;
  background-image: url("../assets/images/login-bg.jpg");
  background-size: cover;
  justify-content: center;
  align-items: center;
}

// .title {
//   margin: 0px auto 30px auto;
//   text-align: center;
//   color: #707070;
// }

// .login-form {
//   border-radius: 6px;
//   background: #ffffff;
//   width: 400px;
//   padding: 25px 25px 5px 25px;

//   .el-input {
//     height: 40px;

//     input {
//       height: 40px;
//     }
//   }

//   .input-icon {
//     height: 39px;
//     width: 14px;
//     margin-left: 0px;
//   }
// }

// .login-tip {
//   font-size: 13px;
//   text-align: center;
//   color: #bfbfbf;
// }

// .login-code {
//   width: 33%;
//   height: 40px;
//   float: right;

//   img {
//     cursor: pointer;
//     vertical-align: middle;
//   }
// }

// .el-login-footer {
//   height: 40px;
//   line-height: 40px;
//   position: fixed;
//   bottom: 0;
//   width: 100%;
//   text-align: center;
//   color: #fff;
//   font-family: Arial, serif;
//   font-size: 12px;
//   letter-spacing: 1px;
// }

// .login-code-img {
//   height: 40px;
//   padding-left: 12px;
// }

.login-content {
  display:flex;
  flex-direction:column;
  align-items:center;
  justify-content: center;
  // margin-top: 288px;
  padding:24px 0;
  border: 2px solid #FFF;
  background-color: rgba(255, 255, 255, 0.70);
  .login-logo {
    height:40px;
  }
  .feishu-btn {
    width:327px;
    height:48px;
    background: linear-gradient(90deg, #3E1DFF 0%, #8E4BFF 29.5%, #D94EEA 52%, #EE518F 82.5%);
    color: #fff;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    .login-icon {
      width:20px;
      height:20px;
      margin-right:4px;
    }
  }
}
.login-form-new {
  width:375px;
  margin-top:80px;
  padding:0 24px;
  ::v-deep {
    .el-input__wrapper {
      // background:#FFFFFF !important;
      width:275px!important;
      // color: #BFBFBF;
    }

    .el-input {
      --el-input-border:#FFFFFF !important;
    }
    .el-button{
      --el-button-hover-text-color:#2551F2;
      --el-button-border-color: #2551F2 !important;
      --el-button-hover-border-color: #2551F2;
    }
  }
  .banck-feishu-btn {
    width:100% ;
    margin:20px 0 0 0;
    text-align: center;
    font-size: 14px;
    height: 40px;
    line-height: 40px;
    color: #2551F2;
    border-radius: 4px;
    border: 1px solid #2551F2;
    &:hover{
      color: #5174F5;
      border: 1px solid #5174F5;
    }
  }

}

.skeleton {
  padding: 16px;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.skeleton-item {
  height: 30px;
  margin: 0 30px;
  margin-top: 30px;
  border-radius: 14px;
  background: #F3F4F5;
  background-size: 400% 100%;
  -webkit-transition: all 0.3s 1s ease-out;
  transition: all 0.3s 1s ease-out;
}
.skeleton-item:first-child{
  width: 30%;
}
.skeleton-item:last-child {
  width: 50%;
}
</style>
