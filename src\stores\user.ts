// @ts-nocheck
import { defineStore } from "pinia";
import { ref } from 'vue';
import {getToken, setToken, removeToken, setSuperToken, getSuperToken} from '@/utils/auth';
import to from 'await-to-js';
import { login as login<PERSON><PERSON>, logout as logout<PERSON><PERSON>, getInfo as getUserInfo, callback as callback<PERSON><PERSON>, appletLoginApi } from '@/api/login';
import defAva from '@/assets/images/profile.jpg';

export const useUserStore = defineStore('user', () => {
    const token = ref(getToken());
    const superToken = ref(getSuperToken());
    const name = ref<string>('');
    const nickName = ref('');
    const userName = ref('');
    const userId = ref<string | number>('');
    const email = ref<string>('');
    const avatar = ref('');
    const roles = ref<Array<string>>([]); // 用户角色编码集合 → 判断路由权限
    const permissions = ref<Array<string>>([]); // 用户权限编码集合 → 判断按钮权限
    const deptId = ref<any>('');
    const info = ref<any>({});

    // 获取用户信息
    const getInfo = async (): Promise<void> => {
        const [err, res]: [any, any] = await to(getUserInfo());
        if (res) {
            const data = res.data;
            const user = data.user;
            localStorage.setItem('userName', user.userName)
            const profile = user.avatar || user.socialAvatar || defAva;

            // 飞书登录埋点
            window.collectEvent('config', {
                user_unique_id: user.userId
              });
            window.collectEvent('profileSet', {
                Wiki_Name: user.userName,
                Wiki_Nickname: user.nickName
            })

            if (data.roles && data.roles.length > 0) {
                // 验证返回的roles是否是一个非空数组
                roles.value = data.roles;
                permissions.value = data.permissions;
            } else {
                roles.value = ['ROLE_DEFAULT'];
            }

            console.log(user, 'useruseruser')
            name.value = user.userName;
            nickName.value = user.nickName;
            userName.value = user.userName;
            avatar.value = profile;
            userId.value = user.userId;
            email.value = user.email;
            deptId.value = user.deptId;
            info.value = user;
            return Promise.resolve();
        }
        return Promise.reject(err);
    };

    // 注销
    const logout = async (): Promise<void> => {
        await logoutApi();
        token.value = '';
        roles.value = [];
        permissions.value = [];
        email.value = '';
        deptId.value = '';
        removeToken();
    };

    const appletLogin = async (userInfo: any): Promise<void> => {
        const [err, res]: [any, any] = await to(appletLoginApi(userInfo));
        if (res) {
            const data = res.data;
            setToken(data.access_token);
            token.value = data.access_token;

            await getInfo();
            return Promise.resolve();
        }
        return Promise.reject(err);
    };

    //  飞书登录成功返回信息
    const feishuLogin = async (data: any): Promise<void> => {
        const [err, res]: [any, any] = await to(callbackApi(data));
        if (res) {
            const data = res.data;
            setToken(data.access_token);
            token.value = data.access_token;
            return Promise.resolve();
        }
        return Promise.reject(err);
    };

    /**
     * 登录
     * @param userInfo
     * @returns
     */
    const login = async (userInfo: any): Promise<void> => {
        const [err, res]: [any, any] = await to(loginApi(userInfo));
        if (res) {
            const data = res.data;
            setToken(data.access_token);
            token.value = data.access_token;
            return Promise.resolve();
        }
        return Promise.reject(err);
    };


    /**
     * 超管登录
     * @param userInfo
     * @returns
     */
    const adminLogin = async (userInfo: any): Promise<void> => {
        const [err, res]: [any, any] = await to(loginApi(userInfo));
        if (res) {
            const data = res.data;
            setSuperToken(data.access_token);
            superToken.value = data.access_token;
            return Promise.resolve();
        }
        return Promise.reject(err);
    };

    return {
        userId,
        email,
        name,
        token,
        nickName,
        userName,
        avatar,
        roles,
        permissions,
        deptId,
        info,
        superToken,
        login,
        getInfo,
        logout,
        feishuLogin,
        appletLogin,
        adminLogin,
    };
})

export default useUserStore;
