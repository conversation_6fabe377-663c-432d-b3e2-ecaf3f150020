<template>
  <div class="hot w-full rounded-[16px] py-[11px] px-[12px] min-h-[400px]">
    <div class="pb-[4px]">
      <img :src="hotTitleIcon" class="min-w-[64px] h-[24px]" />
<!--      <div class="text-[20px] font-500 text-[#17191F]">热门搜索</div>-->
    </div>
    <div class="text-[#B6B7BA] text-[12px] mb-[-2px]">{{ currentDay() }}&nbsp;&nbsp;{{ getWeekday(dayjs(new Date)) }}
    </div>
    <div v-if="noAuth" class="text-center pt-[120px]">
      <img :src="noAuthImg" class="w-[90px] h-[90px] mx-auto">
      <div class="text-[#C9CDD4] text-[14px]">暂无权限</div>
    </div>
    <div v-else>
      <div v-if="list.length > 0" class="flex justify-between items-center cursor-pointer mt-[9.5px]" :key="item.docId"
           v-for="(item, index) in list" @click="handleToDetail(item.docId)">
        <div class="flex items-center gap-[12px]">
          <div v-if="index === 0" class="text-[#F53F3F] text-[16px] font-bold italic">{{ index + 1 }}</div>
          <div v-else-if="index === 1" class="text-[#FF7D00] text-[16px] font-bold italic">{{ index + 1 }}</div>
          <div v-else-if="index === 2" class="text-[#FFB537] text-[16px] font-bold italic">{{ index + 1 }}</div>
          <div v-else class="text-[#B6B7BA] text-[16px] font-bold italic">{{ index + 1 }}</div>
          <div class="text-[#65676B] text-[14px] w-[190px] truncate">{{ item.title }}</div>
          <!-- 调试时查看点击量用 -->
          <!-- <div class="text-[#65676B] text-[14px] truncate">{{ item.sum }}</div> -->
        </div>
        <div>
          <el-icon size="12">
            <ArrowRight style="color: #C9CDD4" />
          </el-icon>
        </div>
      </div>
      <div v-else class="text-[#C9CDD4] text-[14px] mt-[25px] mb-[18px] flex flex-col justify-center items-center ">
        <SvgIcon :width="48" :height="48" class="mb-[8px]">
          <noDataSvg />
        </SvgIcon>
        暂无内容
      </div>
    </div>
  </div>
</template>
<script setup>
import { onMounted, ref } from "vue";
import { useRouter, useRoute } from 'vue-router';
import { ArrowRight } from '@element-plus/icons-vue';
import hotTitleIcon from '@/assets/images/title-hot-search.png';
import hotBg from '@/assets/images/hot-bg.png';
import dayjs from 'dayjs';
import {tagPageApi, eventApi, spaceListAPi} from '@/api/wiki';
import { isToday, getWeekday } from "@/utils";
import SvgIcon from "@/components/SvgIcon.vue";
import noDataSvg from "@/assets/images/svgIcons/noData.vue";
import noAuthImg from '@/assets/images/no-auth.png';

const router = useRouter();

const list = ref([]);
const noAuth = ref(false);

const currentDay = () => {
  return dayjs(new Date).format('YYYY年MM月DD日')
}

// const getList = async () => {
//   const res = await tagPageApi({
//     pageSize: 10,
//     pageNum: 1
//   });
//   if(res.code === 200) {
//     list.value = res.data?.rows || []
//   }
// }

import { getHotDsl } from './dsl'
const testGetEvent = async () => {
  const hotDsl = await getHotDsl()
  const res = await eventApi({ queryBody: JSON.stringify(hotDsl) })
  if (res.code === 200) {
    // 展示文章标题、点击次数
    const hotListData = JSON.parse(res.data).data[0].data_item_list.map(item => {
      return {
        title: item.event_params.Wiki_article_title,
        sum: item.sum,
        docId: item.event_params.Wiki_article_id
      }
    }).filter(item => !item.title.startsWith('{_rawValue'))  // 过滤掉title以{_rawValue开头的数据
    list.value = hotListData
    console.log("热搜列表", hotListData)
  }
}

const handleToDetail = (docId) => {
  router.push({
    path: '/detail',
    query: {
      id: docId
    }
  })
}

const getSpaceList = async () => {
  try {
    const res = await spaceListAPi({
      isAdmin: false
    });
    const data = res?.data?.filter(d => d.isVisible);
    if(!data?.length) {
      noAuth.value = true
    }
  } catch (e) {

  }
}

onMounted(() => {
  // getList();
  testGetEvent();
  getSpaceList()
})
</script>
<style lang="scss" scoped>
.hot {
  //background: linear-gradient( 180deg, #FFECCB 0%, #FFFFFF 17%);
  background-image: url("@/assets/images/hot-bg.png");
  background-size: 100% auto;
  background-repeat: no-repeat;
  background-color: white;
}
</style>
