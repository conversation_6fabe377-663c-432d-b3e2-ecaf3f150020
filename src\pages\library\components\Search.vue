<template>
  <div class="header pb-[24px]">
    <div class="pt-[36px] pb-[24px]">
      <img :src="logo" class="w-[142px] h-[32px] mx-auto" />
    </div>
    <div class="pb-[20px] text-center relative w-[800px] mx-auto">
      <input
          v-model="value"
          placeholder="搜索海量内容，从这里开始" class="text-[16px] w-[800px] h-[48px] pl-[16px] pr-[60px] rounded-[8px] border border-[1px] border-solid border-[#17191F]"
          @keyup.enter="search"
          style="box-shadow: 0 6px 16px 0 rgba(163,163,163,0.45);"
      />
      <el-icon class="absolute right-[20px] top-[10px] cursor-pointer" size="26" @click="search"><Search /></el-icon>
    </div>
    <div class="mb-[16px] w-[800px] mx-auto text-[14px]" v-show="searchFlag">共检索到 <span class="text-[#FFB537]">{{ count }}</span> 条结果</div>
<!--    <div class=" w-[1000px] mx-auto min-h-[220px]">-->
<!--      <Screen @screen="handleScreen" />-->
<!--    </div>-->
  </div>
</template>
<script setup>
import {computed, ref} from 'vue';
import { useRoute } from "vue-router";
import logo from '@/assets/images/logo.png';
import bg1 from '@/assets/images/bg-1.png'
import { Search } from '@element-plus/icons-vue';
import Screen from '@/components/Screen.vue';
import { useCounterStore } from '@/stores/counter';
const counterStore = useCounterStore()
const route = useRoute();

const count = computed(() => counterStore.count)

const emits = defineEmits(['search'])
const value = ref(route.query.keyWord);
const tagIdList = ref([]);
const searchFlag = ref(!!route.query.keyWord);

const change = e => {
  console.log(e.target.value, 'eee')
}

const search = () => {
  searchFlag.value = true;
  emits('search', { keyWord: value.value, tagIdList: tagIdList.value })
}

const handleScreen = (e) => {
  tagIdList.value = e;
  searchFlag.value = true;
  console.log(e, '標簽Id list')
  emits('search', { keyWord: value.value, tagIdList: tagIdList.value })

}
</script>
<style scoped>
  .header {
    background-image: url("@/assets/images/full-bg.png");
    background-repeat: no-repeat;
    background-size: cover;
  }
</style>
