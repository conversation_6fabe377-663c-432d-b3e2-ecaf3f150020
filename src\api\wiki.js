// /wiki/tag/node/{parentId}

import request from '@/utils/request';
// 属性字典 获取子节点（pc）
export function tagNodeApi(parentId) {
  return request({
    url: `/wiki/tag/node/${parentId || 0}`,
    method: 'get'
  });
}

// 搜索列表
export function documentPageApi(params) {
  return request({
    url: `/wiki/document/page`,
    method: 'post',
    data: params
  });
}

// 查询文章详情
export function documentDetailApi(documentId) {
  return request({
    url: `/wiki/document/${documentId}`,
    method: 'get'
  });
}

// 收藏列表
// export function favoriteListApi(params) {
//   return request({
//     url: `/wiki/favorite/list`,
//     method: 'get',
//     params
//   });
// }

// 收藏/取消收藏(点赞/取消点赞)
export function modifyFavoriteApi({type,docId}) {
  return request({
    url: `/wiki/favorite/${type}/${docId}`,
    method: 'put'
  });
}

// 最近浏览
export function viewPageAPi(params) {
  return request({
    url: `/wiki/view/page`,
    method: 'get',
    params
  });
}

// 热门接口
export function tagPageApi(params) {
  return request({
    url: `/wiki/tag/page`,
    method: 'get',
    params
  });
}

// 查看OSS对象存储
export function ossFileInfoApi(ossIds) {
  return request({
    url: `/wiki/oss-file/tenant/${ossIds}`,
    method: 'get'
  });
}

// 收藏列表
export function favoriteListApi(data) {
  return request({
    url: `/wiki/favorite/list`,
    method: 'post',
    data
  });
}

// 获取点赞收藏总数
export function favoriteSizeApi() {
  return request({
    url: `/wiki/favorite/size`,
    method: 'get'
  });
}

// 查询空间列表
export function spaceListAPi(data = {}) {
  return request({
    url: `/wiki/space/list`,
    method: 'post',
    data
  });
}

// 根据空间id获取标签树
export function spaceTagsApi(spaceId) {
  return request({
    url: `/wiki/tag/node/all/${spaceId}`,
    method: 'get'
  });
}

// 查询最新上传的文章
export function newDocListApi() {
  return request({
    url: `/wiki/document/new`,
    method: 'get'
  });
}

// 埋点数据查询
export function eventApi(data) {
  return request({
    url: `/itg/volcengine/api/get-analysis`,
    method: 'post',
    data
  });
}

/**
 * 批量查询文章详情
 * @param {*} documentIds string[] || number[]
 * @returns
 */
export function documentBatchApi(documentIdList) {
  return request({
    url: `/wiki/document/listByIds`,
    method: 'post',
    data: { documentIdList }
  });
}

// 查询专栏信息
export function documentSpecialApi(data) {
  return request({
    url: `/wiki/document/special`,
    method: 'get'
  });
}

// 根据专栏查询专栏文章
export function specialPageApi(data) {
  return request({
    url: `/wiki/document/special/page`,
    method: 'post',
    data
  });
}

// 插入操作日志埋点
export function docLogOperateApi(data) {
  return request({
    url: `/wiki/doc/log/operate/insert`,
    method: 'post',
    data
  });
}

// 查询操作日志
export function docLogOperateQueryApi(data) {
  return request({
    url: `/wiki/doc/log/operate/query`,
    method: 'post',
    data
  });
}

// 下载OSS对象存储
export function downloadApi(ossId) {
  return request({
    url: `/wiki/oss-file/download/tenant/${ossId}`,
    method: 'post'
  });
}
