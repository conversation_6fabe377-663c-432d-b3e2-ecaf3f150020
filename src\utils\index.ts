import { ElMessage } from "element-plus";
import { AxiosResponse } from "axios";
import {removeToken} from "./auth";
// @ts-ignore
import dayjs from "dayjs";

/**
 *   跳转登录
 */
export const jumpLogin = () => {
    // vm.$Cookies.remove("vue_admin_token");
    // vm.$router.push(`/login?redirect=${encodeURIComponent(vm.$route.fullPath)}`);
    removeToken();
    // @ts-ignore
    window.location.href = import.meta.env.VITE_APP_FEISHU_AUTH_URL;
};

/**
 * 下载文件
 * @param response
 * @returns
 */
export const downloadFile = (response: AxiosResponse) => {
    console.log("response.data.type:", response.data.type);
    return new Promise((resolve, reject) => {
        const fileReader = new FileReader();
        fileReader.onload = function () {
            try {
                console.log("result:", this.result);
                const jsonData = JSON.parse((this as any).result); // 成功 说明是普通对象数据
                if (jsonData?.code !== 200) {
                    ElMessage.error(jsonData?.message ?? "请求失败");
                    reject(jsonData);
                }
            } catch (err) {
                // 解析成对象失败，说明是正常的文件流
                const blob = new Blob([response.data]);
                // 本地保存文件
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement("a");
                link.href = url;
                const filename = response?.headers?.["content-disposition"]
                    ?.split("filename*=")?.[1]
                    ?.substr(7);
                link.setAttribute("download", decodeURI(filename));
                document.body.appendChild(link);
                link.click();
                resolve(response.data);
            }
        };
        fileReader.readAsText(response.data);
    });
};

const weekdayNames = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];

export const getWeekday = (date: any) => {
    const weekday = dayjs(date).day();
    return weekdayNames[weekday];
}

export const isToday = (date: any) => dayjs().isSame(date, 'day');

export const copyText = (id, cb) => {
    // 获取要复制的文本
    const text = document.getElementById(id)?.value;

    try {
        // 使用现代 Clipboard API
        navigator.clipboard.writeText(text).then(() => {
            if(cb) {
                cb()
            }
        });
    } catch (err) {
        // 兼容旧版方法
        const textArea = document.createElement("textarea");
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();

        try {
            document.execCommand("copy");
            if(cb) {
                cb()
            }
        } catch (err) {
            alert("复制失败，请手动复制");
        }

        document.body.removeChild(textArea);
    }
}
