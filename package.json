{"name": "gensci-wiki", "description": "GenWiKi C端", "version": "1.2.0", "repository": {"type": "git", "url": "https://github.com/feitian124/vue3-tailwind3-website-starter.git"}, "keywords": [], "author": "feitian124 <feitian124.com>", "license": "MIT", "bugs": {"url": "https://github.com/feitian124/vue3-tailwind3-website-starter/issues"}, "homepage": "https://github.com/feitian124/vue3-tailwind3-website-starter", "scripts": {"dev": "vite --mode development", "preview-biuld": "vite --mode production", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production", "preview": "vite build && vite preview", "start": "yarn dev & wait-on tcp:3000 -v", "test": "vitest", "test-e2e": "cypress open", "test-components": "cypress open-ct", "test:ci": "vitest", "test:ci-e2e": "cypress run --headless", "test:ci-components": "cypress run-ct"}, "resolutions": {"string-width": "4.2.0", "strip-ansi": "6.0.0"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@pdf-lib/fontkit": "^1.1.1", "@vue-office/docx": "^1.6.3", "@vueuse/core": "~7.7.0", "@vueuse/head": "~0.7", "await-to-js": "^3.0.0", "daisyui": "^4.12.14", "dayjs": "^1.11.13", "downloadjs": "^1.4.7", "element-plus": "^2.9.4", "file-saver": "2.0.5", "pdf-lib": "^1.17.1", "pinia": "~2.0.11", "pinia-plugin-persistedstate": "^4.2.0", "vue": "~3.2", "vue-demi": "^0.14.10", "vue-i18n": "9", "vue-router": "~4.0"}, "devDependencies": {"@cypress/vite-dev-server": "~2.2", "@cypress/vue": "~3.0", "@intlify/vite-plugin-vue-i18n": "~3.3.1", "@pinia/testing": "~0.0.9", "@tailwindcss/aspect-ratio": "~0.4", "@tailwindcss/forms": "~0.4", "@tailwindcss/line-clamp": "~0.3", "@tailwindcss/typography": "~0.5", "@typescript-eslint/eslint-plugin": "~5", "@typescript-eslint/parser": "~5", "@vitejs/plugin-vue": "~2.2.4", "@vue/eslint-config-standard": "~6.1", "@vue/eslint-config-typescript": "~10", "autoprefixer": "~10", "cssnano": "~5.1.3", "cypress": "~9.5.0", "eslint": "~8.8", "eslint-config-prettier": "~8.3", "eslint-plugin-import": "~2", "eslint-plugin-node": "~11.1", "eslint-plugin-promise": "~6.0", "eslint-plugin-vue": "~8", "postcss": "~8.4", "postcss-import": "~14.0.2", "postcss-nesting": "~10", "sass": "^1.83.0", "sass-loader": "^16.0.4", "tailwindcss": "~3", "typescript": "~4.5", "unplugin-vue-components": "~0.17.18", "vite": "~2.7", "vite-plugin-pages": "~0.20.2", "vite-plugin-vue-layouts": "~0.6.0", "vitest": "~0.6.0", "wait-on": "~6.0"}}