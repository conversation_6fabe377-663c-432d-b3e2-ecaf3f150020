<template>
  <div>
    <div class="w-full h-screen bg-white pt-[220px] text-center" v-if="noAuth">
      <img :src="noAuthImg" class="w-[200px] h-[200px] mx-auto" />
      <div class="text-[#17191F] my-[14px] text-[20px]">
        抱歉，您当前无权限查看此文件
      </div>
      <div class="text-[#86909C] text-[16px]">
        若您有查看的需求，请联系系统管理员申请权限。
        <br />
        感谢理解!
      </div>
    </div>
    <div v-else>
      <div class="w-[1340px] mx-auto bg-white rounded-[16px] px-[16px] my-[16px] pb-[16px]">
        <div class="breadcrumbs text-sm pt-[10px]">
          <ul>
            <li class=""><a class="text-[#4E595E]">首页</a></li>
            <li class="font-bold">详情页</li>
          </ul>
        </div>
        <div class="flex gap-[10px] items-center pb-[16px]">
          <el-icon class="cursor-pointer" @click="handleBack" v-if="route.query.isShare !== '1'">
            <ArrowLeftBold />
          </el-icon>
          <div class="text-[#17191F] text-[24px] break-words font-600">
            {{ fileInfo.title }}
          </div>
        </div>
        <div class="flex justify-between pb-[18px]">
          <div class="flex gap-[8px] flex-wrap">
            <div class="px-[4px] py-[2px] bg-[#F2F3F5] text-[#65676B] text-[12px] rounded-[2px]" :key="tag.id" v-for="tag in [
              ...(fileInfo.wikiTagVoList || []),
              ...(fileInfo.customizeTag || []),
            ]">
              # {{ tag.tagName }}
            </div>
          </div>
          <div class="flex gap-[16px] min-w-[150px] text-right">
            <!--            <span class="text-[#B6B7BA] text-[12px] ">2024-12-12</span>-->
            <!--            <span class="text-[#B6B7BA] text-[12px] ">阅读1.2k</span>-->
          </div>
        </div>
        <div class="pb-[10px]" v-if="fileInfo.summary">
          <span class="text-[#17191F] text-[14px] leading-[16px]">摘要：</span>
          <span class="text-[#65676B] text-[14px] leading-[16px] break-words">{{
            fileInfo.summary
          }}</span>
        </div>
        <div class="bg-[#E5E6EB] h-[1px] w-full mb-[16px]"></div>
        <div v-if="noFile">
          <Empty text="文件已失效~" />
        </div>
        <div>
          <!-- 预览，根据资料类型以不同方式展示 -->
          <video v-if="isVideo(ossFileInfo?.url)" id="video" :src="ossFileInfo?.url" initial-time="0" :controls="true"
            :autoplay="false" :loop="false" :muted="false" style="
              height: calc(100vh - 400px);
              min-height: 600px;
              width: 100%;
              position: relative;
            " />
          <iframe v-else-if="pdfUrl" :src="basePDFUrl + pdfUrl"
            style="height: 100vh; border: none; width: 100%"></iframe>
          <iframe v-else-if="pptxUrl" :src="basePPTXUrl + pptxUrl"
            style="height: 100vh; border: none; width: 100%"></iframe>
          <!-- docx文件预览,使用vue-office -->
          <!-- <div>docx预览{{ ossFileInfo.url }}</div> -->
          <vue-office-docx v-else-if="isDocx(ossFileInfo)" :src="ossFileInfo.url" style="height: 100vh; width: 100%"
            @rendered="renderedHandler" @error="errorHandler" />
          <!-- <hr /> -->
          <img :src="ossFileInfo?.url" class="w-full" v-else />
        </div>
      </div>

      <div class="py-[12px] w-[50px] bg-white rounded-[50px] fixed bottom-[150px] right-[55px]" style="
          box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.1);
          border: 1px solid #e5e6eb;
        ">
        <div class="text-center cursor-pointer" @click="(e) => handleFav(fileInfo, e)">
          <img :src="zanIcon" v-if="!fileInfo.isLike" class="w-[20px] h-[20px] cursor-pointer mx-auto" />
          <img :src="zanedIcon" v-else class="w-[20px] h-[20px] cursor-pointer mx-auto" />
          <div class="text-[#86909C] text-[11px] h-[20px] leading-[20px]" v-if="!fileInfo.isLike">
            点赞
          </div>
          <div class="text-[#FFB537] text-[11px] h-[20px] leading-[20px]" v-else>
            点赞
          </div>
        </div>
        <div class="h-[1px] bg-[#F2F3F5] mt-[10px] my-[4px]"></div>
        <div class="text-center cursor-pointer" @click="(e) => handleCollect(fileInfo, e)">
          <img :src="favIcon" v-if="!fileInfo.isCollect" class="w-[20px] h-[20px] cursor-pointer mx-auto" />
          <img :src="startedIcon" v-else class="w-[20px] h-[20px] cursor-pointer mx-auto" />
          <div class="text-[#86909C] text-[11px] h-[20px] leading-[20px]" v-if="!fileInfo.isCollect">
            收藏
          </div>
          <div class="text-[#FFB537] text-[11px] h-[20px] leading-[20px]" v-else>
            收藏
          </div>
        </div>

        <div class="h-[1px] bg-[#F2F3F5] mt-[10px] my-[4px]"></div>
        <div class="text-center cursor-pointer" @click="handleShare(fileInfo)">
          <img :src="shareImg" class="w-[20px] h-[20px] cursor-pointer mx-auto" />
          <div class="text-[#86909C] text-[11px] h-[20px] leading-[20px]">
            转发
          </div>
        </div>

        <!-- 默认展示下载按钮 -->
        <div>
          <div class="h-[1px] bg-[#F2F3F5] mt-[10px] my-[4px]"></div>
          <div class="text-center cursor-pointer" @click="handleDownload(fileInfo)">
            <img :src="downloadImg" class="w-[20px] h-[20px] cursor-pointer mx-auto" />
            <div class="text-[#86909C] text-[11px] h-[20px] leading-[20px]">
              下载
            </div>
          </div>
        </div>
      </div>
    </div>

    <ShareDialog ref="shareDialogRef" />

    <el-backtop :right="55" :bottom="100"> </el-backtop>
  </div>
</template>
<script setup>
import { onMounted, ref, getCurrentInstance, computed } from "vue";
//引入VueOfficeDocx组件
import VueOfficeDocx from "@vue-office/docx";
//引入相关样式
import "@vue-office/docx/lib/index.css";

import { useRouter, useRoute } from "vue-router";
import { documentDetailApi, downloadApi, modifyFavoriteApi } from "@/api/wiki";
import { ossFileInfoApi, docLogOperateApi } from "../../api/wiki";
import backTopImg from "@/assets/images/backTop.png";

import favIcon from "@/assets/images/fav_icon.png";
import startedIcon from "@/assets/images/started.png";
import zanIcon from "@/assets/images/zan-icon.png";
import zanedIcon from "@/assets/images/zan-fill-icon.png";
import { ElMessage } from "element-plus";
import { trackArticleView } from "./tracking";
import noAuthImg from "@/assets/images/no-auth.png";
import downloadImg from "@/assets/images/download.png";
import shareImg from "@/assets/images/zhuanfa_icon.png";
import useUserStore from "@/stores/user";
import { onBeforeRouteLeave } from "vue-router";
import { ArrowLeftBold } from "@element-plus/icons-vue";
import Empty from "@/components/Empty.vue";
import { handleDownload } from "@/utils/download";
import useSpaceStore from "@/stores/space";

const spaceStore = useSpaceStore();


const userStore = useUserStore();

const { proxy } = getCurrentInstance();

const route = useRoute();
const router = useRouter();

const basePDFUrl = ref("/pdfjs/web/viewer.html?file=");
const basePPTXUrl = ref("/pptx_viewer/index.html?url=");
// const pdfUrl = ref('https://athena-dev.dgtmeta.com/pdfjs/web/viewer.html?file=https%3A%2F%2Fathena-obs-dev.gsims.com.cn%2F2024%2F12%2F22%2Fc13bbddaff204070aa04531070da47e3.pdf')
const pdfUrl = ref("");
const pdfFullUrl = ref("");
const originalName = ref("");
const pptxUrl = ref("");
const fileInfo = ref({});
const logInitParams = ref({});
const ossFileInfo = ref({});
const loading = ref(false);
const noFile = ref(false);
const noAuth = ref(false);
const shareDialogRef = ref(null);

const getDetail = async () => {
  try {
    if (!route.query.id) return false;
    loading.value = true;
    const { code, data, msg } = await documentDetailApi(route.query.id);
    if (code === 200) {
      fileInfo.value = data;
      logInitParams.value = {
        content: fileInfo.value?.title,
        systemTerminal: "PC",
        spaceName: fileInfo.value?.spaceName,
        documentId: fileInfo.value?.id,
      };

      if (msg === "暂无权限查看") {
        noAuth.value = true;
        return false;
      }

      if (!data.id) {
        noFile.value = true;
        return false;
      }

      if (!data.ossId) return false;
      const ossFileRes = await ossFileInfoApi(data.ossId);
      if (ossFileRes.code === 200) {
        const ossFile = ossFileRes?.data?.rows?.[0];
        ossFileInfo.value = ossFile;
        if (ossFile) {
          const { fileSuffix, url } = ossFile;
          if (fileSuffix === ".pdf" || fileSuffix === ".PDF") {
            pdfUrl.value = encodeURIComponent(url);
            pdfFullUrl.value = url;
            originalName.value = ossFile.originalName;
          } else if (fileSuffix === ".pptx") {
            pptxUrl.value = encodeURIComponent(url);
          }
        }
      }
    }
    loading.value = false;
  } catch (e) {
    noFile.value = true;
  } finally {
    loading.value = false;
  }
};

const handleBack = () => {
  router.back();
};

const handleFav = async (row, e) => {
  e?.stopPropagation();
  // 1收藏 2点赞
  await modifyFavoriteApi({
    docId: row.id,
    type: 2,
  });
  if (row.isLike) {
    ElMessage.success("取消点赞！");
  } else {
    ElMessage.success("点赞成功！");
  }
  await reload();
};

const handleCollect = async (row, e) => {
  e?.stopPropagation();
  // 1收藏 2点赞
  await modifyFavoriteApi({
    docId: row.id,
    type: 1,
  });
  if (row.isCollect) {
    ElMessage.success("取消收藏！");
  } else {
    ElMessage.success("收藏成功！");
  }
  await reload();
};

// 文件类型判断方法
const isDocx = (fileInfo) => {
  // console.log('fileInfoType', fileInfo?.fileSuffix === '.docx' || fileInfo?.fileSuffix === '.DOCX')
  return fileInfo?.fileSuffix === ".docx" || fileInfo?.fileSuffix === ".DOCX";
};
// 预览docx文件时，渲染完成和渲染失败的回调
const renderedHandler = () => {
  console.log("渲染完成");
};
const errorHandler = () => {
  console.log("渲染失败");
};

const isVideo = (fileUrl) => {
  if (!fileUrl) return false;
  const videoFormats = ["mp4", "webm", ".3gp", ".avi"];
  const regex = new RegExp(videoFormats.join("|"), "i");
  return regex.test(fileUrl.toLowerCase());
};

const reload = async () => {
  try {
    if (!route.query.id) return false;
    const { code, data } = await documentDetailApi(route.query.id);
    if (code === 200) {
      fileInfo.value = data;
    }
  } catch (e) {
    noFile.value = true;
  } finally {
  }
};

const insertLog = async () => {
  // 操作类型（新增：insert，修改：update，删除：delete，查看：view，分享：share 下载: download）
  // 有文件权限时才记录访问日志
  if (logInitParams.value?.spaceName && logInitParams.value?.content) {
    await docLogOperateApi({
      logType: "view",
      ...logInitParams.value,
    });
  }
};

const handleShare = async (row) => {
  shareDialogRef.value?.open(row);
};

onMounted(async () => {
  await getDetail();
  await insertLog();
  // 开始埋点
  // console.log('fileInfo', fileInfo)
  trackArticleView.start(fileInfo.value);
});
// 离开页面时，停止埋点
onBeforeRouteLeave((to, from, next) => {
  // 记录阅读时长
  trackArticleView.end();
  next();
});
</script>
