<template>
  <div class="flex gap-[4px] flex-wrap items-center">
    <div class="h-[24px] px-[8px] leading-[24px] text-[#FF8800] bg-[#FFF3E6] rounded-[2px] text-[12px]" v-for="item in props.data" :key="item.id" :style="{color: styleMap[item.tagName].color, backgroundColor: styleMap[item.tagName].bg}">
      {{ item.tagName }}
    </div>
  </div>
</template>
<script setup>
const props = defineProps({
  data: {
    type: Array,
    default: []
  }
});

const styleMap = {
  '产品知识': {
    color: '#FF8800',
    bg: '#FFF3E6'
  },
  '专家观念': {
    color: '#2551F2',
    bg: '#E8F0FF'
  },
  '科普内容': {
    color: '#F7BA1E',
    bg: '#FFFCE8'
  },
  '合规指引': {
    color: '#722ED1',
    bg: '#F5E8FF'
  },
  '指南共识': {
    color: '#33D1C9',
    bg: '#E8FFFB'
  },
  '研究文献': {
    color: '#33D1C9',
    bg: '#E8FFFB'
  },
}
</script>
