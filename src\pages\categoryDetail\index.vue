<template>
  <div
    class="bg-[length:100%_340px] bg-no-repeat"
    :style="{ backgroundImage: `url(${currentCate.bgUrl})` }"
  >
    <div
      class="w-[1200px] mx-auto mb-[24px] pb-[18px] relative"
      style="min-height: calc(100vh - 209px)"
    >
      <div
        class="absolute right-0 top-[24px] text-center cursor-pointer"
        @click="handleToSpecial()"
        v-if="route.query.spaceName === '金赛增专栏'"
      >
        <img :src="dayImg" class="w-[54px] h-[58px]" />
        <div class="text-[#17191F] text-[16px]">日日谈</div>
      </div>
      <div class="breadcrumbs text-sm py-[17px]">
        <ul>
          <li class="text-white">首页</li>
          <li class="text-white">{{ route.query.spaceName }}</li>
        </ul>
      </div>
      <div class="flex justify-center">
        <div class="flex items-center gap-[8px]">
          <SvgIcon
            :width="58"
            :height="58"
            viewBox="0 0 58 58"
            class="relative top-[2px]"
          >
            <BookIcon />
          </SvgIcon>
          <div class="text-[46px] text-white">{{ route.query.spaceName }}</div>
        </div>
      </div>
      <div
        class="w-[400px] h-[2px] mx-auto my-[12px]"
        style="
          background: linear-gradient(
            90deg,
            rgba(255, 255, 255, 0) 0%,
            #fff 55%,
            rgba(255, 255, 255, 0) 100%
          );
        "
      ></div>
      <div class="text-[16px] text-white text-center font-500">
        {{ currentCate.text }}
      </div>
      <div class="flex justify-center my-[28px]">
        <img
          v-if="currentCate.aiImg"
          :src="currentCate.aiImg"
          class="h-[52px] cursor-pointer"
          :style="{ width: currentCate.aiImgW }"
          @click="handleToAI()"
        />
      </div>
      <div
        class="px-[24px] py-[32px] rounded-[16px] flex justify-center"
        style="
          background: linear-gradient(
            180deg,
            rgba(255, 255, 255, 0.6) 0%,
            #ffffff 100%
          );
          border: 2px solid #fff;
        "
      >
        <div
          v-if="
            userScreen.nodeList?.length &&
            route.query.spaceName === '金妍迪科-女性健康专栏'
          "
          :class="`flex flex-wrap relative left-[46px] mt-[-16px]`"
        >
          <div
            @click="handleClick(item)"
            :class="`flex items-center gap-[4px] px-[12px] cursor-pointer flex-wrap mt-[16px] w-[16%] ${
              checkedTagIdList.includes(item.id) ? 'checked' : ''
            } ${
              item.tagName === '金舒安' || item.tagName === '无瘤防御'
                ? 'pl-[40px]'
                : ''
            }
              ${
                (item.tagName === '全部' || item.tagName === '美适亚') &&
                'pl-[40px]'
              }
            `"
            v-for="(item, index) in [
              { tagName: '全部', id: '-1' },
              ...userScreen.nodeList,
            ]"
            :key="item.id"
          >
            <div
              class="h-[24px] w-[24px] flex items-center justify-center rounded-[50%]"
              :style="{ backgroundColor: tagsStyle(item).bgColor }"
            >
              <SvgIcon :height="16" :width="16" viewBox="0 0 16 16">
                <Component
                  :is="
                    route.query.spaceName === '科普专栏'
                      ? AllIcon
                      : tagsStyle(item).icon
                  "
                />
              </SvgIcon>
            </div>
            <div
              :class="`hover:text-[#FFB537] cursor-pointer text-[20px] font-500`"
            >
              {{ item.tagName }}
            </div>
          </div>
        </div>
        <div
          v-else-if="userScreen.nodeList?.length"
          :class="`flex flex-wrap ${
            route.query.spaceName === '科普专栏' ||
            route.query.spaceName === '金妍迪科-女性健康专栏'
              ? 'relative left-[46px] mt-[-16px]'
              : ''
          } ${
            route.query.spaceName === '金妍迪科-生殖健康专栏'
              ? 'gap-[30px]'
              : 'gap-[8px]'
          }`"
        >
          <div
            @click="handleClick(item)"
            :class="`flex items-center gap-[4px] px-[12px] cursor-pointer ${
              checkedTagIdList.includes(item.id) ? 'checked' : ''
            } ${
              route.query.spaceName === '科普专栏'
                ? 'w-[19%] flex-wrap mt-[16px]'
                : ''
            } ${
              route.query.spaceName === '金妍迪科-女性健康专栏'
                ? 'w-[16%] flex-wrap mt-[16px]'
                : ''
            }`"
            v-for="item in [
              { tagName: '全部', id: '-1' },
              ...userScreen.nodeList,
            ]"
            :key="item.id"
          >
            <div
              class="h-[24px] w-[24px] flex items-center justify-center rounded-[50%]"
              :style="{ backgroundColor: tagsStyle(item).bgColor }"
            >
              <SvgIcon :height="16" :width="16" viewBox="0 0 16 16">
                <Component
                  :is="
                    route.query.spaceName === '科普专栏'
                      ? AllIcon
                      : tagsStyle(item).icon
                  "
                />
              </SvgIcon>
            </div>
            <div
              :class="`hover:text-[#FFB537] cursor-pointer text-[20px] font-500`"
            >
              {{ item.tagName }}
            </div>
          </div>
        </div>
      </div>
      <div class="pt-[20px]">
        <List
          ref="listRef"
          :delay="true"
          :showSearch="true"
          :spaceId="route.query.spaceId"
          :hideTotal="false"
        />
      </div>
    </div>
    <Footer />
  </div>
</template>
<script setup>
import { computed, onMounted, ref } from "vue";
import { useRouter, useRoute } from "vue-router";

import { useScreenStore } from "@/stores/screen";
import Footer from "@/components/Footer.vue";
import List from "@/pages/library/components/List.vue";

import jinsaizengBg from "@/assets/images/jinsaizeng_bg.png";
import jinsaizengIcon from "@/assets/images/jinsaizeng_icon.png";
import SvgIcon from "@/components/SvgIcon.vue";
import BookIcon from "@/assets/images/svgIcons/direction/book.vue";
import AllIcon from "@/assets/images/svgIcons/direction/tags/all.vue";
import JBZLIcon from "@/assets/images/svgIcons/direction/tags/jibingzhenliao.vue";
import JSZSMSIcon from "@/assets/images/svgIcons/direction/tags/jinsaizengshuomingshu.vue";
import LCYJIcon from "@/assets/images/svgIcons/direction/tags/linghcuangyanjiu.vue";
import YXBLIcon from "@/assets/images/svgIcons/direction/tags/youxiubingli.vue";
import XGWXIcon from "@/assets/images/svgIcons/direction/tags/xiangguanwenxian.vue";
import ZHPPTIcon from "@/assets/images/svgIcons/direction/tags/zongheppt.vue";
import JSZJPIcon from "@/assets/images/svgIcons/direction/tags/jinsaizengjingpin.vue";
import RRTAIImg from "@/assets/images/rrt_ai.png";
import kpAIImg from "@/assets/images/kp_ds.png";
import dayImg from "@/assets/images/day.png";
import kpBg from "@/assets/images/kp_bg.png";
import szjkBg from "@/assets/images/szjk_zhuanlan_bg.png";

import { useCounterStore } from "@/stores/counter";
const userScreen = useScreenStore();
import { spaceTagsApi, documentSpecialApi } from "@/api/wiki";
const counterStore = useCounterStore();
const router = useRouter();
const route = useRoute();

const count = computed(() => counterStore.count);
const currentCate = computed(() => {
  return cateMap[route.query.spaceName] || {};
});

const currentSpecial = ref({});

const cateMap = {
  金赛增专栏: {
    bgUrl: jinsaizengBg,
    text: "提供全面的金赛增相关资料",
    icon: jinsaizengIcon,
    aiImg: RRTAIImg,
    aiImgW: 259,
    dsUrl:
      "https://base-links.genscigroup.com/chat/share?shareId=qkztykdcqky1bcvzhexq80bj",
  },
  科普专栏: {
    bgUrl: kpBg,
    text: "提供全新的科普相关资料",
    icon: jinsaizengIcon,
    aiImg: kpAIImg,
    aiImgW: 211,
    dsUrl:
      "https://base-links.genscigroup.com/chat/share?shareId=9hiezj393quy4vtblakr7um7",
  },
  "金妍迪科-女性健康专栏": {
    bgUrl: kpBg,
    text: "提供全面的女性健康相关资料",
    icon: jinsaizengIcon,
  },
  "金妍迪科-生殖健康专栏": {
    bgUrl: szjkBg,
    text: "提供全面的生殖健康专栏相关资料",
    icon: jinsaizengIcon,
  },
};

const tagsStyle = (tag) => {
  switch (tag.tagName) {
    case "全部":
      return {
        bgColor: "#FFB537",
        icon: AllIcon,
      };

    case "疾病诊疗":
    case "儿童身高":
    case "达那唑栓":
    case "题库":
    case "金赛恒":
      return {
        bgColor: "#8D4EDA",
        icon: JBZLIcon,
      };

    case "金赛增说明书":
    case "儿童肥胖":
    case "妇科器械":
    case "通泽产品":
    case "金赛捷":
      return {
        bgColor: "#4080FF",
        icon: JSZSMSIcon,
      };

    case "临床研究":
    case "儿童神经&心理":
    case "金赛欣 (妇科)":
    case "无瘤防御":
    case "金赛欣":
      return {
        bgColor: "#FF9A2E",
        icon: LCYJIcon,
      };

    case "优秀病例":
    case "儿童体姿态":
    case "金舒安":
    case "曲普瑞林":
      return {
        bgColor: "#F76560",
        icon: YXBLIcon,
      };

    case "相关文献":
    case "儿童营养":
    case "氯喹":
    case "赛增金赛增":
      return {
        bgColor: "#37D4CF",
        icon: XGWXIcon,
      };

    case "综合PPT":
    case "中西医治疗性早熟":
    case "儿童性发育":
    case "美适亚":
    case "实验室设备":
      return {
        bgColor: "#F9CC45",
        icon: ZHPPTIcon,
      };

    case "金赛增竞品":
    case "其他物料":
    case "仕达思 IVD":
      return {
        bgColor: "#57A9FB",
        icon: JSZJPIcon,
      };

    default:
      return {
        bgColor: "#FFB537",
        icon: AllIcon,
      };
  }
};

const currentTagId = ref("");
const tagList = ref([]);
const checkedTagIdList = ref(
  route.query.tagIdList ? JSON.parse(route.query.tagIdList) : ["-1"]
);
const listRef = ref();
const childTagList = ref([]);

const getTags = async () => {
  try {
    const res = await spaceTagsApi(route.query.spaceId);
    tagList.value = [{ tagName: "全部", id: "-1" }, ...(res.data || [])];
  } catch (e) {}
};

const handleClick = async (item) => {
  if (item.id === "-1") {
    checkedTagIdList.value = ["-1"];
  } else {
    checkedTagIdList.value = [item.id];
  }

  await listRef.value?.getList({
    tagIdList:
      checkedTagIdList.value[0] === "-1" ? ["-1"] : checkedTagIdList.value,
    pageNum: 1,
  });
};

const getSpecial = async () => {
  const res = await documentSpecialApi();
  currentSpecial.value = res?.data?.[0];
};

const handleToSpecial = () => {
  router.push({
    path: "/special",
    query: {
      specialId: currentSpecial.value.specialId,
      specialName: currentSpecial.value.specialName,
    },
  });
};

const handleToAI = () => {
  window.open(currentCate.value.dsUrl, "_blank");
};

onMounted(() => {
  // getTags();
  getSpecial();
  userScreen.getNodeList(route.query.spaceId);
  listRef.value?.getList({ tagIdList: [] });
});
</script>
<style scoped lang="scss">
.checked {
  color: #ffb537 !important;
}

.breadcrumbs > ul > li + *:before,
.breadcrumbs > ol > li + *:before {
  color: #fff !important;
}
</style>
