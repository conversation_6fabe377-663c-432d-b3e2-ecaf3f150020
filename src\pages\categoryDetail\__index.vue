<template>
  <div class="bg-[length:100%_200px] bg-no-repeat" :style="{ backgroundImage: `url(${currentCate.bgUrl})` }">
    <div class="w-[1200px] mx-auto pb-[24px]">
      <div class="breadcrumbs text-sm py-[17px]">
        <ul>
          <li class="text-white">首页</li>
          <li class="text-white">{{ route.query.tagName }}</li>
          <li class="text-white font-bold">详情页</li>
        </ul>
      </div>
      <div class="p-[24px] rounded-[16px]" style="
          background: linear-gradient(
            180deg,
            rgba(255, 255, 255, 0.6) 0%,
            #ffffff 100%
          );
          border: 2px solid #fff;
        ">
        <div class="flex gap-[24px] items-top pb-[24px]">
          <div class="min-w-[122px] w-[122px] h-[178px] flex-none">
            <img :src="currentCate.icon" class="w-full h-full" />
          </div>
          <div class="grow">
            <div class="text-[#17191F] text-[34px] font-600 mb-[8px]">
              {{ route.query.tagName }}
            </div>
            <div class="text-[#65676B] leading-[22px] break-words">
              {{ currentCate.text }}
            </div>
            <!--            <div class="text-[#B6B7BA] text-[14px] pt-[12px]">最近更新：2024-12-01</div>-->
          </div>
          <!--          <div class="text-center flex-none min-w-[120px]">-->
          <!--            <div class="text-[#FFB537] text-[34px] font-500 mb-[2px] h-[40px] leading-[40px]">{{ count }}</div>-->
          <!--            <div class="text-[#999999] text-[14px] h-[22px] leading-[22px]">篇 内容持续更新</div>-->
          <!--          </div>-->
        </div>
        <!-- 标签列表 -->
        <div class="flex items-center">
          <div class="flex-1 overflow-hidden">
            <div class="flex gap-[12px] items-center">
              <div v-for="item in visibleTags" :key="item.id">
                <!-- 当tagName为金赛增，展示特殊标签 -->
                <el-popover v-if="item.tagName === '金赛增'" v-model:visible="isSourcePopoverVisible" placement="bottom-start"
                            :width="480" trigger="hover">
                  <template #reference>
                    <div
                        :class="`cursor-pointer h-[24px] leading-[24px] px-[6px] bg-[#F2F3F5] text-[#65676B] text-[12px] rounded-[2px] ${checkedTagIdList.includes(item.id) ? 'checked' : ''}`">
                      {{ item.tagName }}
                      <SvgIcon :width="12" :height="12" class="inline-block transition-transform duration-300"
                               :class="{ '-rotate-180': isSourcePopoverVisible }">
                        <DownSvg />
                      </SvgIcon>
                    </div>
                  </template>
                  <!-- 浮层内组件 -->
                  <div>
                    <el-row :gutter="10">
                      <!-- 内容来源 -->
                      <el-col class="mb-1.5">
                        <div class="text-[12px] mb-1.5 text-[#86909C]">内容来源</div>
                        <el-checkbox-group v-model="selectedSources" class="grid grid-cols-4 gap-2">
                          <el-checkbox label="培训部" />
                          <el-checkbox label="科普市场" />
                          <!-- 占位元素确保对齐 -->
                          <div></div>
                          <div></div>
                        </el-checkbox-group>
                      </el-col>

                      <!-- 适应症 -->
                      <el-col class="mb-1.5">
                        <div class="text-[12px] mb-1.5 text-[#86909C]">适应症</div>
                        <el-checkbox-group v-model="selectedSymptoms" class="grid grid-cols-4 gap-2">
                          <el-checkbox label="GHD" />
                          <el-checkbox label="CPP" />
                          <el-checkbox label="ISS" />
                          <el-checkbox label="SGA" />
                        </el-checkbox-group>
                      </el-col>

                      <!-- 类型 -->
                      <el-col>
                        <div class="text-[12px] mb-1.5 text-[#86909C]">类型</div>
                        <el-checkbox-group v-model="selectedTypes" class="grid grid-cols-4 gap-2">
                          <el-checkbox label="金赛日日谈" />
                          <el-checkbox label="产品文献" />
                          <el-checkbox label="指南共识" />
                          <!-- 占位元素确保对齐 -->
                          <div></div>
                        </el-checkbox-group>
                      </el-col>
                    </el-row>
                  </div>
                </el-popover>
                <!-- 普通标签 -->
                <div v-else @click="handleClick(item)" :class="`cursor-pointer h-[24px] leading-[24px] px-[6px] bg-[#F2F3F5] text-[#65676B] text-[12px] rounded-[2px] ${checkedTagIdList.includes(item.id) ? 'checked' : ''
                  }`">
                  <span>
                    {{ item.tagName }}
                  </span>
                </div>
              </div>

              <!-- 当标签个数过多时，展示可展开的更多标签 -->
              <el-popover v-if="hasMoreTags" v-model:visible="isPopoverVisible" placement="bottom" :width="300"
                          trigger="hover">
                <template #reference>
                  <div
                      class="flex items-center cursor-pointer h-[24px] leading-[24px] px-[6px] bg-[#F2F3F5] text-[#65676B] text-[12px] rounded-[2px]">
                    更多
                    <SvgIcon :width="12" :height="12" class="transition-transform duration-300"
                             :class="{ '-rotate-90': isPopoverVisible }">
                      <RightSvg />
                    </SvgIcon>
                  </div>
                </template>
                <div class="flex gap-[12px] flex-wrap">
                  <div v-for="item in hiddenTags" :key="item.id" @click="handleClick(item)" :class="`cursor-pointer h-[24px] leading-[24px] px-[6px] bg-[#F2F3F5] text-[#65676B] text-[12px] rounded-[2px] ${checkedTagIdList.includes(item.id) ? 'checked' : ''
                    }`">
                    {{ item.tagName }}
                  </div>
                </div>
              </el-popover>
            </div>
          </div>

          <div class="flex items-center text-[#FFB537] cursor-pointer text-[14px] ml-[24px]" @click="reset()">
            <el-icon size="20">
              <Refresh class="mr-[4px]" />
            </el-icon>重置
          </div>
        </div>
      </div>
      <div class="pt-[20px]">
        <List ref="listRef" :delay="true" :showSearch="true" />
      </div>
    </div>
    <!-- 底部 -->
    <Footer />
  </div>
</template>
<script setup>
import { computed, onMounted, ref, watch } from "vue";
import { useRouter, useRoute } from "vue-router";

import List from "@/pages/library/components/List.vue";
import Footer from '@/components/Footer.vue';
import chanpingzhishi from "@/assets/images/chanpingzhishi.png";
import zhuanjiaguannian from "@/assets/images/zhuanjiaguannian.png";

import kepuneirongImg from "@/assets/images/kepuneirong.png";
import heguizhiyinImg from "@/assets/images/heguizhiyin.png";
import zhinangongshiImg from "@/assets/images/zhinangongshi.png";
import yanjiuwenxianImg from "@/assets/images/yanjiuwenxian.png";

import bg1 from "@/assets/images/chanpingzhishi-bg.jpg";
import bg2 from "@/assets/images/cate_2.png";
import bg3 from "@/assets/images/cate_3.png";
import bg4 from "@/assets/images/cate_6.png";
import bg5 from "@/assets/images/cate_4.png";
import bg6 from "@/assets/images/cate_5.png";

import { useCounterStore } from "@/stores/counter";
import SvgIcon from "@/components/SvgIcon.vue";
import RightSvg from "@/assets/images/svgIcons/direction/right.vue";
import DownSvg from "@/assets/images/svgIcons/direction/down.vue";
import { tagNodeApi } from "../../api/wiki";
const counterStore = useCounterStore();
const router = useRouter();
const route = useRoute();

const count = computed(() => counterStore.count);
const currentCate = computed(() => {
  return cateMap[route.query.tagName];
});

const cateMap = {
  产品知识: {
    bgUrl: bg1,
    text: "提供全面的产品介绍和支持信息，包括产品详情、适应症、使用方法、剂型等",
    icon: chanpingzhishi,
  },
  专家观念: {
    bgUrl: bg2,
    text: "汇集疾病的诊断标准、治疗方案、疗效监测，并了解专家对常见治疗方法、面临的挑战以及药物使用的不同观念",
    icon: zhuanjiaguannian,
  },
  科普内容: {
    bgUrl: bg3,
    text: "科普宣传推广内容、疾病管理干预方案，以及科普活动所需的宣传物料，如宣传视频、三折页、课件等，支持科普活动的顺利开展",
    icon: kepuneirongImg,
  },
  合规指引: {
    bgUrl: bg4,
    text: "汇集合规文件、操作指南以及各种合规模板，便于定期学习和遵守合规要求、法律法规",
    icon: heguizhiyinImg,
  },
  指南共识: {
    bgUrl: bg5,
    text: "收录医学领域的指南、共识和治疗方案，提供较为权威的医学参考，帮助理解跟进当前的治疗标准和未来趋势",
    icon: zhinangongshiImg,
  },
  研究文献: {
    bgUrl: bg6,
    text: "提供文献情报、临床研究、最新资讯等具有科学依据的研究成果",
    icon: yanjiuwenxianImg,
  },
};

const currentTagId = ref("");
const tagList = ref([]);
const checkedTagIdList = ref([]);
const listRef = ref();
const childTagList = ref([]);

const getTags = async () => {
  const tagName = route.query.tagName;
  const res = await tagNodeApi();
  if (res.code === 200) {
    if (res.data && res.data.length) {
      const current = res.data.find((d) => d.tagName === tagName);
      currentTagId.value = current.id;
      if (!childTagList.value.length) {
        const childTagRes = await tagNodeApi(current.id);
        if (childTagRes.code === 200) {
          childTagList.value = (childTagRes.data || []).map((d) => d.id);
          await listRef.value?.getList({ tagIdList: childTagList.value });
          tagList.value = childTagRes.data || [];
        }
      } else {
        await listRef.value?.getList({ tagIdList: childTagList.value });
      }
    }
  }
};

const handleClick = (item) => {
  const _index = checkedTagIdList.value.findIndex((d) => d === item.id);
  if (_index > -1) {
    checkedTagIdList.value.splice(_index, 1);
  } else {
    checkedTagIdList.value.push(item.id);
  }

  if (!checkedTagIdList.value.length) {
    getTags();
  } else {
    listRef.value?.getList({ tagIdList: checkedTagIdList.value });
  }
};

const reset = async () => {
  checkedTagIdList.value = [];
  await getTags();
}

// 根据容器宽度计算可显示的标签数量，这里假设显示15个
const visibleCount = ref(15) // 默认显示15个
// TODO: 通过计算容器宽度和标签宽度计算可显示的标签数量
const visibleTags = computed(() => {
  return tagList.value.slice(0, visibleCount.value)
})

const hiddenTags = computed(() => {
  return tagList.value.slice(visibleCount.value)
})

const hasMoreTags = computed(() => {
  return tagList.value.length > visibleCount.value
})

const isPopoverVisible = ref(false)

// 金赛增标签的内容来源弹窗
const isSourcePopoverVisible = ref(false)
const selectedSources = ref([])
const selectedSymptoms = ref([])
const selectedTypes = ref([])

// 监听所有选项的变化
watch([selectedSources, selectedSymptoms, selectedTypes], ([sources, symptoms, types]) => {
  const jszItem = visibleTags.value.find(item => item.tagName === '金赛增')
  if (!jszItem) return

  const hasSelection = sources.length > 0 || symptoms.length > 0 || types.length > 0
  const _index = checkedTagIdList.value.findIndex(d => d === jszItem.id)

  if (hasSelection && _index === -1) {
    checkedTagIdList.value.push(jszItem.id)
  } else if (!hasSelection && _index > -1) {
    checkedTagIdList.value.splice(_index, 1)
  }

  if (!checkedTagIdList.value.length) {
    getTags()
  } else {
    listRef.value?.getList({ tagIdList: checkedTagIdList.value })
  }
}, { deep: true })

onMounted(() => {
  getTags();
});
</script>
<style scoped lang="scss">
.checked {
  color: #fff !important;
  background: #ffb537 !important;
}

.breadcrumbs>ul>li+*:before,
.breadcrumbs>ol>li+*:before {
  color: #fff !important;
}
</style>
