<template >
  <div class="flex justify-between items-center" v-if="total > 0">
    <div class="text-[14px]" v-if="!props.hideTotal">共检索到 <span class="text-[#FFB537] px-[8px]">{{ total }}</span>条结果</div>
    <div v-else></div>
    <div class="flex gap-[8px] items-center">
      <img :src="disArrowImg" class="w-[28px] h-[28px] cursor-not-allowed" v-if="props.currentPage === 1">
      <img :src="arrowImg" class="w-[28px] h-[28px] cursor-pointer rotate-180" v-else @click="handlePre()">
      <div class="flex gap-[8px] items-center">
        <div class="min-w-[24px] text-center text-[#4E595E] font-600">{{ props.currentPage }}</div>
        <div class="min-w-[24px] text-center">/</div>
        <div class="min-w-[24px] text-center">{{ Math.ceil(total / pageSize) }}</div>
      </div>
      <img :src="disArrowImg" class="w-[28px] h-[28px] cursor-not-allowed rotate-180" v-if="props.currentPage === Math.ceil(total / pageSize)">
      <img :src="arrowImg" class="w-[28px] h-[28px] cursor-pointer" v-else @click="handleNext()">
      <el-select v-model="pageSize" class="min-w-[110px]" @change="pageSizeChange">
        <el-option
            v-for="item in pages"
            :key="item.value"
            :label="item.label"
            :value="item.value"
        />
      </el-select>
    </div>
  </div>
</template>
<script setup>
import {ref, toRefs} from 'vue';
import disArrowImg from '@/assets/images/pagination-prev.png';
import arrowImg from '@/assets/images/pagination-next.png';

const props = defineProps({
  total: {
    type: Number || String,
    default: 0
  },
  currentPage: {
    type: Number,
    default: 1
  },
  hideTotal: {
    type: Boolean,
    default: false
  }
})

const { total } = toRefs(props);
const emits = defineEmits(['sizeChange', 'currentChange']);
const pageSize = ref(10);
const current = ref(1);
const pages = ref([
  {
    label: '10条/页',
    value: 10
  },
  {
    label: '20条/页',
    value: 20
  },
  {
    label: '30条/页',
    value: 30
  },
  {
    label: '40条/页',
    value: 40
  },
  {
    label: '50条/页',
    value: 50
  },
]);

const pageSizeChange = (e) => {
  // 分页大小变化 current默认1
  emits('update:current-page', 1)
  emits('update:page-size', e);
  emits('sizeChange', e);
}

const currentChange = (e) => {
  emits('update:current-page', e)
  emits('currentChange', e);
}

const handleNext = () => {
  emits('update:current-page', props.currentPage + 1)
  emits('currentChange', props.currentPage + 1);
}

const handlePre = () => {
  emits('update:current-page', props.currentPage - 1)
  emits('currentChange', props.currentPage - 1);
}

</script>
<style scoped>

</style>
