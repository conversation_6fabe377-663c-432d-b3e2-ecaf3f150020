<template>
  <div class="w-[1200px] mx-auto">
    <div class="breadcrumbs text-sm pt-[16px]">
      <ul>
        <li class="text-[#4E595E]">首页</li>
        <li class="text-[#1D212B]">最近浏览</li>
      </ul>
    </div>
    <div class="w-[800px] mx-auto relative top-[-16px]">
      <div class="text-[#FFB537] text-[14px] mb-[16px] cursor-pointer flex items-center gap-[4px]" @click="goBack()">
        <img :src="backImg" class="w-[12px] h-[12px]" >
        <span>返回</span>
      </div>

      <div class="rounded-[16px] pb-[8px] bg-white mb-[16px]" v-for="(value, key) in groupByList" :key="key">
        <div class="px-[24px] py-[16px] text-[#17191F] text-[18px]" style="border-bottom: 1px solid #E5E6EB">
<!--          {{ dayjs(new Date()).format('YYYY-MM-DD') }}-->
          {{ isToday(dayjs(key)) ? '今天-' : ''}}
            {{ dayjs(key).format('YYYY年MM月DD日') }} {{getWeekday(key)}}
          </div>
          <div class="flex gap-[40px] items-center pt-[16px] pl-[24px] pr-[12px]" v-for="item in value" :key="item.docId">
            <div class="text-[#B6B7BA] text-[14px]">{{ dayjs(item.lastOpenTime).format('HH:mm') }}</div>
            <div class="text-[#17191F] text-[14px] underline cursor-pointer truncate flex-[12]" @click="handleToDetail(item)">{{ item.title }}</div>
            <TagList :data="[...(item.wikiTagVoList || []), ...(item.customizeTag || [])]" class="flex-[7]" />
          </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {computed, onMounted, ref} from 'vue';
import dayjs from "dayjs";
import { eventApi, documentBatchApi } from '@/api/wiki';
import TagList from '@/components/TagListShort.vue';
import backImg from '@/assets/images/back-icon.png';
import {useRouter} from "vue-router";
import { isToday, getWeekday } from "@/utils";
// import {historyDsl} from '../index/components/dsl'
import {getHistoryDsl} from '../index/components/dsl'

const router = useRouter();
const list = ref([]);

const getList = async () => {
  const historyDsl = await getHistoryDsl()
  const res = await eventApi({
    queryBody: JSON.stringify(historyDsl)
  });
  if(res.code === 200) {
    const parsedData = JSON.parse(res.data);
    const historyData = parsedData.data[0].data_item_list
    .map(item => ({
      title: item.event_params.Wiki_article_title,
      docId: item.event_params.Wiki_article_id,
      lastOpenTime: dayjs(Math.floor(item.sum)).format('YYYY-MM-DD HH:mm:ss')
    }))
    .filter(item => !item.title.startsWith('{_rawValue'));
    // 获取文档详情
    const documentDetails = await documentBatchApi(historyData.map(item => item.docId));
    console.log('documentDetails', documentDetails);
    // 合并文档详情的tag信息
    list.value = historyData.map(item => {
      const detail = documentDetails.data.find(doc => doc.id === item.docId);
      return {
        ...item,
        wikiTagVoList: detail?.wikiTagVoList || [], // 使用wikiTagVoList作为标签数据
        // title: detail?.title || item.title
      };
    });
    console.log('list', list.value);
    // list.value = historyData;
  }
}

const groupByList = computed(() => {
    const groupedDocuments = {};

  list.value.forEach((document) => {
      const lastOpenTime = document.lastOpenTime.split(' ')[0]; // 提取日期部分作为分组依据
      if (groupedDocuments[lastOpenTime]) {
        groupedDocuments[lastOpenTime].push(document);
      } else {
        groupedDocuments[lastOpenTime] = [document];
      }
    });

    return groupedDocuments;
})

const handleToDetail = (row) => {
  router.push({
    path: '/detail',
    query: {
      id: row.docId,
    }
  })
}

const goBack = () => {
  router.back()
}

onMounted(() => {
  getList();
})

</script>
