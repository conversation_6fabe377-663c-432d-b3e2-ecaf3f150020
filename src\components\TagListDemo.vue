<!-- 该文件是结合了TagList和TagListShort的组件，通过参数maxVisibleTags决定是否隐藏更多标签，暂未验证，未使用 -->
<template>
  <div class="flex gap-[4px]" :class="{'flex-nowrap': isShort, 'flex-wrap': !isShort}">
    <template v-for="(tag, index) in displayTags" :key="tag.id">
      <div class="px-[4px] py-[2px] text-[12px] rounded-[2px]" 
           :style="showTagColor ? getTagStyle(parentTagName) : defaultStyle">
        # {{ tag.tagName }}
      </div>
    </template>
    <div v-if="isShort && hasMoreTags"
      class="px-[4px] py-[2px] text-[12px] rounded-[2px] relative"
      @mouseenter="showTooltip = true" 
      @mouseleave="showTooltip = false">
      <SvgIcon :width="12" :height="12" color="#C9CDD4" viewBox="0 0 12 12" class="inline-block">
        <MoreSvg />
      </SvgIcon>
      <!-- 浮层 -->
      <div v-if="showTooltip"
        class="absolute left-0 bottom-[100%] mb-[8px] bg-white shadow-lg rounded-[4px] p-[8px] z-10">
        <div v-for="tag in hiddenTags" :key="tag.id" class="whitespace-nowrap mb-[4px] last:mb-0">
          # {{ tag.tagName }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useScreenStore } from '@/stores/screen';
import { computed, ref } from 'vue';
import SvgIcon from '@/components/SvgIcon.vue';
import MoreSvg from '@/assets/images/svgIcons/more.vue';

const userScreen = useScreenStore();
const showTooltip = ref(false);

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  showTagColor: {
    type: Boolean,
    default: false
  },
  parentTagName: {
    type: String,
    default: ''
  },
  maxVisibleTags: {
    type: Number || Null,
    default: null
  }
});
const isShort = props.maxVisibleTags !== null;
const displayTags = computed(() => {
  if (!props.isShort) return props.data;
  return props.data.slice(0, props.maxVisibleTags);
});

const hiddenTags = computed(() => {
  if (!props.isShort) return [];
  return props.data.slice(props.maxVisibleTags);
});

const hasMoreTags = computed(() => {
  return props.isShort && props.data.length > props.maxVisibleTags;
});

const defaultStyle = {
  backgroundColor: '#F2F3F5',
  color: '#65676B'
};

const styleMap = {
  '金赛增说明书': { color: '#fff', bg: '#4080FF' },
  '临床研究': { color: '#fff', bg: '#FF9A2E' },
  '优秀病例': { color: '#fff', bg: '#F76560' },
  '相关文献': { color: '#fff', bg: '#37D4CF' },
  '综合PPT': { color: '#fff', bg: '#F9CC45' },
  '金赛增竞品': { color: '#fff', bg: '#57A9FB' },
  '': { color: '#FF8800', bg: '#FFF3E6' }
};

const getTagStyle = (tagName) => {
  const style = styleMap[tagName] || styleMap[''];
  return {
    color: style.color,
    backgroundColor: style.bg
  };
};
</script>

<style>
em {
  color: #FFB537 !important;
  font-style: normal;
}
</style>