<template>
  <div>
    <div style="min-height: calc(100vh - 184px)">
      <div class="header h-[200px] flex items-center">
        <div class="w-[1200px] mx-auto flex items-center justify-between">
          <img :src="defaultIpIcon" class="w-[64px] h-[64px]">
          <div class="ml-[24px] text-[#1D2129] text-[24px] font-500 flex-1">{{ userStore.nickname }}</div>
          <div class="flex gap-[90px]">
            <div class="text-center">
              <div class="h-[40px] leading-[40px] text-[#FFB537] text-[34px]">{{ size.collectSize }}</div>
              <div class="h-[22px] leading-[22px] text-[#999999] text-[14px]">收藏</div>
            </div>
            <div class="text-center">
              <div class="h-[40px] leading-[40px] text-[#FFB537] text-[34px]">{{ size.likeSize }}</div>
              <div class="h-[22px] leading-[22px] text-[#999999] text-[14px]">点赞</div>
            </div>
          </div>
        </div>
      </div>
      <div class="bg-white">
        <div class="w-[1200px] mx-auto bg-white h-[64px] flex items-center justify-between gap-[20px]">
          <div class="text-[#17191F] text-[20px]">我的收藏</div>
<!--          <div class="flex-1">-->
<!--            <div class="flex gap-[12px] flex-wrap">-->
<!--              <div @click="handleClick(item)" :class="`cursor-pointer h-[24px] leading-[24px] px-[6px] text-[#65676B] text-[12px] rounded-[2px] ${checkedTagList.map(d => d.id).includes(item.id) ? 'checked' : ''}`" v-for="item in topSearchList" :key="item.id">{{ item.tagName }}</div>-->
<!--            </div>-->
<!--          </div>-->
          <div>
            <el-input
                v-model="keyWord"
                class="w-[220px] h-[32px] leading-[32px]"
                placeholder="输入内容查询"
                :suffix-icon="Search"
                maxlength="100"
                style="background: #F5F5F5; border-radius: 24px"
                @keyup.enter="getList(true)"
            />
          </div>
        </div>
      </div>
      <div class="w-[1200px] mx-auto mt-[24px] mb-[24px] pb-[18px]">
        <div>
          <el-table :data="list" style="width: 100%">
            <el-table-column prop="title" label="标题" show-overflow-tooltip>
              <template #default="scope">
                <div class="text-[#17191F] font-400 text-[14px] underline cursor-pointer max-w-[400px] truncate" @click="handleToDetail(scope.row)">{{ scope.row.title }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="分类" label="分类" width="200">
              <template #default="scope">
                <div class="mr-[8px]">
                  <ParentTagList :data="scope.row.parentTagVoList || []" />
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="标签" label="标签" width="160" show-overflow-tooltip>
              <template #default="scope">
                {{ scope.row?.wikiTagVoList?.map(d => d.tagName ).join('、')}}
              </template>
            </el-table-column>
            <el-table-column prop="发布日期" label="发布日期" width="160">
              <template #default="scope">
                <div>
                  {{ dayjs(scope.row.createTime).format('YYYY-MM-DD')}}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="action" label="操作" width="160">
              <template #default="scope">
                <div class="flex gap-[20px]">
                  <!--            <img :src="zhuanfaIcon" class="w-[16px] h-[16px] cursor-pointer" />-->
                  <!--            <img :src="downloadIcon" class="w-[16px] h-[16px] cursor-pointer" />-->
                  <img :src="startedIcon" class="w-[16px] h-[16px] cursor-pointer" @click="e => handleFav(scope.row, e)" />
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="mt-[16px] flex justify-end">
          <el-pagination
              v-model:current-page="pageNum"
              v-model:page-size="pageSize"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :total="total"
              layout="prev, pager, next, sizes, jumper"
              :background="false"
              size="small"
          />
        </div>
      </div>
    </div>
    <Footer />
  </div>
</template>
<script setup>
import {onMounted, ref} from 'vue';
import dayjs from "dayjs";
import {useRouter} from "vue-router";
import { Search } from '@element-plus/icons-vue';
import defaultIpIcon from '@/assets/images/default-ip-icon.png';
import ParentTagList from '@/components/ParentTagList.vue';
import startedIcon from '@/assets/images/started.png';
import { useUserStore } from '@/stores/user';
import {favoriteListApi, favoriteSizeApi, modifyFavoriteApi, tagNodeApi} from "@/api/wiki";
import {ElMessage} from "element-plus";
import Footer from '@/components/Footer.vue';
import Pagination from '@/components/Pagination.vue';
const userStore = useUserStore();
const router = useRouter();

const keyWord = ref('');
const pageSize = ref(10);
const pageNum = ref(1);
const total = ref(0);
const list = ref([]);
const loading = ref(false);
const tagIdList = ref([]);
const topSearchList = ref([]);
const checkedTagList = ref([{id: '-1'}]) // 默认查询全部 用作前端展示 接口处要过滤掉-1

const size = ref({
  likeSize: 0,
  collectSize: 0
});

const handleSizeChange = (val) => {
  console.log(`${val} items per page`)
  getList()
}
const handleCurrentChange = (val) => {
  console.log(`current page: ${val}`)
  getList()
}

const reduceIds = (data) => {
  return data.reduce((result, item) => {
    if(item.level !== '1') {
      result.push(item.id);
    }
    if (item.children) {
      result.push(...reduceIds(item.children));
    }
    return result;
  }, []);
};

const getList = async (isSearch) => {
  try {
    loading.value = true;
    if(isSearch) {
      pageNum.value = 1
    }
    const res = await favoriteListApi({
      pageSize: pageSize.value,
      pageNum: pageNum.value,
      keyWord: keyWord.value,
      tagIdList: reduceIds(checkedTagList.value).filter(d => d !== '-1'),
    });
    if(res) {
      list.value = res?.data?.list;
      total.value = res?.data?.total;
      // counterStore.increment(data.total)
    }
  } catch (e) {

  } finally {
    loading.value = false;
  }
}

const favoriteTotal = async () => {
  // {"likeSize":0,"collectSize":1}}
  const res = await favoriteSizeApi();
  if(res.code === 200) {
    size.value = res?.data || {}
  }

}

const handleFav = async (row, e) => {
  e?.stopPropagation();
  // 1收藏 2点赞
  await modifyFavoriteApi({
    docId: row.docId,
    type: 1
  });
  if(row.isCollect) {
    ElMessage.success('取消收藏！')
  }else {
    ElMessage.success('收藏成功！')
  };
  await getList();
}

const getTopNodes = async () => {
  const res = await tagNodeApi();
  for (const item of (res.data || [])) {
    const childRes = await tagNodeApi(item.id);
    if(childRes.data) {
      item.children = childRes.data
    }
  }
  topSearchList.value = [{tagName: '全部', id: '-1'},...(res.data || [])];
}

const handleClick = item => {
  if(item.id !== '-1') {
    const _allIndex = checkedTagList.value.findIndex(d => d.id === '-1');
    if(_allIndex > -1) {
      checkedTagList.value.splice(_allIndex, 1)
    }
  }else if(item.id === '-1') {
    checkedTagList.value = [];
  }

  const _index = checkedTagList.value.findIndex(d => d.id === item.id);
  if(_index > -1) {
    checkedTagList.value.splice(_index, 1)
  }else {
    checkedTagList.value.push(item)
  }
  // if(!checkedTagList.value.length) {
  //   checkedTagList.value.push({
  //     id: '-1'
  //   })
  // }

  getList()
}

const handleToDetail = (row, e) => {
  e?.stopPropagation();
  router.push({
    path: '/detail',
    query: {
      id: row.docId,
      ossId: row.ossId
    }
  })
}


onMounted(() => {
  getList();
  favoriteTotal();
  getTopNodes();
})

</script>
<style scoped lang="scss">
.checked {
  color: #fff !important;
  background: #FFB537 !important;
}
.header {
  background-image: url("@/assets/images/bg-1.png");
  background-repeat: no-repeat;
  background-size: cover;
}

::v-deep(input) {
  border: none;

}
::v-deep(input[type='text']:focus) {
  outline: none;
  box-shadow: none;
  border: none;
  font-size: 14px;
}

::v-deep(.el-input__wrapper) {
  border-radius: 24px;
  box-shadow: none;
  background: #F5F5F5;
}
::v-deep(.el-input__inner) {
  background: #F5F5F5;
}

::v-deep(.el-table th.el-table__cell) {
  background-color: #F7F8FA;
  color: #1D212B;
}

::v-deep(.el-pagination__jump .el-input__inner) {
  background: #fff;
}

::v-deep(.el-pagination__jump .el-input__inner) {
  border-radius: 8px;
}
</style>
