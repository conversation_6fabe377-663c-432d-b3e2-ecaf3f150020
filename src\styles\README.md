# 样式文件说明

## download-confirm.css

下载申请确认弹框的自定义样式文件。

### 使用方法

在主入口文件（如 `main.js` 或 `main.ts`）中引入：

```javascript
import './styles/download-confirm.css'
```

或者在需要使用的组件中引入：

```javascript
import '@/styles/download-confirm.css'
```

### 样式特性

1. **现代化设计**：
   - 圆角边框 (12px)
   - 柔和阴影效果
   - 清晰的视觉层次

2. **响应式布局**：
   - 桌面端：固定宽度，居中显示
   - 移动端：自适应宽度，按钮垂直排列

3. **交互优化**：
   - 按钮悬停效果
   - 清晰的视觉反馈
   - 符合 Element Plus 设计规范

### 自定义类名

- `.download-apply-confirm` - 主容器类名
- 可通过 CSS 变量或覆盖样式进行自定义

### 兼容性

- 支持现代浏览器
- 兼容 Element Plus 组件库
- 响应式设计，支持移动端
