// 下载申请参数构建示例

// 示例用户信息（基于您提供的数据结构）
const exampleUserInfo = {
  "userId": "1868554249369452583",
  "tenantId": "000080",
  "deptId": "1868552625909563417",
  "userName": "GS5738",
  "nickName": "张浩威",
  "userType": "sys_user",
  "email": "<EMAIL>",
  "phoneNumber": "186****0012",
  "sex": "0",
  "avatar": null,
  "socialAvatar": "https://s1-imfile.feishucdn.com/static-resource/v1/v2_538d7c1a-6d9c-4321-95e0-49646200808g~?image_size=72x72&cut_type=&quality=&format=image&sticker_format=.webp",
  "status": "0",
  "loginIp": "**************",
  "loginDate": "2025-08-07 10:50:55",
  "remark": null,
  "createTime": "2024-12-16 15:10:05",
  "dept": {
    "deptId": "1868552625909563417",
    "parentId": "1868552625909563411",
    "parentName": null,
    "ancestors": "0,1868552625909563416,1868552625909563411",
    "deptName": "后端开发部",
    "deptType": null,
    "deptMgrNum": null,
    "deptMgrName": null,
    "orderNum": 0,
    "leader": null,
    "leaderName": null,
    "phone": null,
    "email": null,
    "status": "1",
    "createTime": null,
    "parentDeptCode": "GSZN1602",
    "deptCode": "GSZN160203",
    "deptLevel": "3",
    "isChildren": null,
    "children": null
  },
  "roles": [],
  "posts": [
    {
      "postId": "1868553095700971545",
      "postCode": "GSZN16F030",
      "postName": "Web工程师",
      "postSort": null,
      "status": "0",
      "remark": null,
      "createTime": "2024-12-16 15:10:05",
      "parentPostCode": "GSZN16D006",
      "parentId": "1868552625909563411",
      "ancestors": "0,1868553078328164379,1868553095373815843,1868553095700971535,1868553095700971544"
    }
  ],
  "roleIds": null,
  "postIds": null,
  "roleId": null
};

// 示例文件信息
const exampleFileInfo = {
  id: 123,
  title: "重要文档.pdf",
  spaceId: 456,
  type: "1",
  downloadType: "2"
};

// 构建下载申请参数的函数
export const buildDownloadApplyParams = (fileInfo, userInfo) => {
  const dept = userInfo.dept || {};
  const posts = userInfo.posts || [];
  const primaryPost = posts[0] || {}; // 取第一个岗位作为主岗位
  
  return {
    docId: fileInfo.id,
    spaceId: fileInfo.spaceId,
    // 申请人信息
    applicant: userInfo.nickName || userInfo.userName,
    applicantCode: userInfo.userName,
    // 岗位信息
    postCode: primaryPost.postCode || '',
    postName: primaryPost.postName || '',
    // 部门信息
    deptCode: dept.deptCode || '',
    deptName: dept.deptName || ''
  };
};

// 运行示例
export const runExample = () => {
  console.log('=== 下载申请参数构建示例 ===');
  console.log('用户信息:', exampleUserInfo);
  console.log('文件信息:', exampleFileInfo);
  
  const applyParams = buildDownloadApplyParams(exampleFileInfo, exampleUserInfo);
  console.log('构建的申请参数:', applyParams);
  
  // 验证参数完整性
  const requiredFields = ['docId', 'spaceId', 'applicant', 'applicantCode', 'postCode', 'postName', 'deptCode', 'deptName'];
  const missingFields = requiredFields.filter(field => !applyParams[field]);
  
  if (missingFields.length === 0) {
    console.log('✅ 所有必需参数都已正确填充');
  } else {
    console.log('❌ 缺少以下参数:', missingFields);
  }
  
  return applyParams;
};

// 测试不同用户信息场景
export const testDifferentUserScenarios = () => {
  console.log('\n=== 测试不同用户信息场景 ===');
  
  // 场景1：完整用户信息
  console.log('\n--- 场景1：完整用户信息 ---');
  runExample();
  
  // 场景2：缺少部门信息
  console.log('\n--- 场景2：缺少部门信息 ---');
  const userWithoutDept = { ...exampleUserInfo, dept: null };
  const params2 = buildDownloadApplyParams(exampleFileInfo, userWithoutDept);
  console.log('构建的申请参数:', params2);
  
  // 场景3：缺少岗位信息
  console.log('\n--- 场景3：缺少岗位信息 ---');
  const userWithoutPosts = { ...exampleUserInfo, posts: [] };
  const params3 = buildDownloadApplyParams(exampleFileInfo, userWithoutPosts);
  console.log('构建的申请参数:', params3);
  
  // 场景4：只有userName，没有nickName
  console.log('\n--- 场景4：只有userName，没有nickName ---');
  const userWithoutNickName = { ...exampleUserInfo, nickName: '' };
  const params4 = buildDownloadApplyParams(exampleFileInfo, userWithoutNickName);
  console.log('构建的申请参数:', params4);
};

export default {
  exampleUserInfo,
  exampleFileInfo,
  buildDownloadApplyParams,
  runExample,
  testDifferentUserScenarios
};
