<template>
  <div
    class="bg-[length:100%_340px] bg-no-repeat"
    :style="{ backgroundImage: `url(${currentCate.bgUrl})` }"
  >
    <div class="w-[1200px] mx-auto mb-[24px] pb-[18px] relative" style="min-height: calc(100vh - 209px)">
<!--      <div class="absolute right-0 top-[24px]　text-center cursor-pointer" @click="handleToSpecial()">-->
<!--        <img :src="dayImg" class="w-[54px] h-[58px]">-->
<!--        <div class="text-[#17191F] text-[16px]">日日谈</div>-->
<!--      </div>-->
      <div class="breadcrumbs text-sm py-[17px]">
        <ul>
          <li class="text-white">首页</li>
          <li class="text-white">{{ route.query.spaceName }}</li>
        </ul>
      </div>
      <div class="flex justify-center">
        <div class="flex items-center gap-[8px]">
          <SvgIcon :width="58" :height="58" viewBox="0 0 58 58" class="relative top-[2px]">
            <BookIcon />
          </SvgIcon>
          <div class="text-[46px] text-white">{{ route.query.spaceName }}</div>
        </div>
      </div>
      <div class="w-[400px] h-[2px] mx-auto my-[12px]" style="background: linear-gradient(90deg, rgba(255, 255, 255, 0.00) 0%, #FFF 55%, rgba(255, 255, 255, 0.00) 100%);">
      </div>
      <div class="text-[16px] text-white text-center font-500">
        提供全面的金赛增相关资料
      </div>
      <div class="flex justify-center my-[28px]">
        <img :src="RRTAIImg" class="w-[259px] h-[52px] cursor-pointer" @click="handleToAI()">
      </div>
      <div
        class="px-[24px] py-[32px] rounded-[16px] flex justify-center"
        style="
          background: linear-gradient(
            180deg,
            rgba(255, 255, 255, 0.6) 0%,
            #ffffff 100%
          );
          border: 2px solid #fff;
        "
      >

        <div class="flex gap-[8px] flex-wrap">
          <div
            @click="handleClick(item)"
            :class="`flex items-center gap-[4px] px-[12px] cursor-pointer ${
              checkedTagIdList.includes(item.id) ? 'checked' : ''
            }`"
            v-for="item in [{tagName: '全部', id: '-1'}, ...userScreen.nodeList]"
            :key="item.id"
          >
            <div class="h-[24px] w-[24px] flex items-center justify-center rounded-[50%]" :style="{ backgroundColor: tagsStyle(item).bgColor}">
              <SvgIcon :height="16" :width="16" viewBox="0 0 16 16">
                <Component :is="tagsStyle(item).icon" />
              </SvgIcon>
            </div>
            <div :class="`hover:text-[#FFB537] cursor-pointer text-[20px] font-500`">
              {{ item.tagName }}
            </div>
          </div>
        </div>
      </div>
      <div class="pt-[20px]">
        <List ref="listRef" :delay="true" :showSearch="true" :spaceId="route.query.spaceId" :hideTotal="false" />
      </div>
    </div>
    <Footer />
  </div>
</template>
<script setup>
import { computed, onMounted, ref } from "vue";
import { useRouter, useRoute } from "vue-router";

import { useScreenStore } from '@/stores/screen';
import Footer from '@/components/Footer.vue';
import List from "@/pages/library/components/List.vue";

import jinsaizengBg from '@/assets/images/jinsaizeng_bg.png';
import jinsaizengIcon from '@/assets/images/jinsaizeng_icon.png';
import SvgIcon from "@/components/SvgIcon.vue";
import BookIcon from '@/assets/images/svgIcons/direction/book.vue';
import AllIcon from '@/assets/images/svgIcons/direction/tags/all.vue';
import JBZLIcon from '@/assets/images/svgIcons/direction/tags/jibingzhenliao.vue';
import JSZSMSIcon from '@/assets/images/svgIcons/direction/tags/jinsaizengshuomingshu.vue';
import LCYJIcon from '@/assets/images/svgIcons/direction/tags/linghcuangyanjiu.vue';
import YXBLIcon from '@/assets/images/svgIcons/direction/tags/youxiubingli.vue';
import XGWXIcon from '@/assets/images/svgIcons/direction/tags/xiangguanwenxian.vue';
import ZHPPTIcon from '@/assets/images/svgIcons/direction/tags/zongheppt.vue';
import JSZJPIcon from '@/assets/images/svgIcons/direction/tags/jinsaizengjingpin.vue';
import RRTAIImg from '@/assets/images/rrt_ai.png';
import dayImg from '@/assets/images/day.png';

import { useCounterStore } from "@/stores/counter";
const userScreen = useScreenStore();
import {spaceTagsApi, documentSpecialApi} from "@/api/wiki";
const counterStore = useCounterStore();
const router = useRouter();
const route = useRoute();

const count = computed(() => counterStore.count);
const currentCate = computed(() => {
  return cateMap[route.query.spaceName] || {};
});

const currentSpecial = ref({});

const cateMap = {
  '金赛增专栏': {
    bgUrl: jinsaizengBg,
    text: "提供全面的金赛增相关资料",
    icon: jinsaizengIcon,
  }
};

const tagsStyle = (tag) => {
  switch (tag.tagName) {
    case '全部' :
      return {
        bgColor: '#FFB537',
          icon: AllIcon
      }

    case '疾病诊疗' :
      return {
        bgColor: '#8D4EDA',
        icon: JBZLIcon
      }

    case '金赛增说明书' :
      return {
        bgColor: '#4080FF',
        icon: JSZSMSIcon
      }

    case '临床研究' :
      return {
        bgColor: '#FF9A2E',
        icon: LCYJIcon
      }

    case '优秀病例' :
      return {
        bgColor: '#F76560',
        icon: YXBLIcon
      }

    case '相关文献' :
      return {
        bgColor: '#37D4CF',
        icon: XGWXIcon
      }


    case '综合PPT' :
      return {
        bgColor: '#F9CC45',
        icon: ZHPPTIcon
      }

    case '金赛增竞品' :
      return {
        bgColor: '#57A9FB',
        icon: JSZJPIcon
      }

    default :
      return {
        bgColor: '#FFB537',
        icon: AllIcon
      }
  }
}

const currentTagId = ref("");
const tagList = ref([]);
const checkedTagIdList = ref(['-1']);
const listRef = ref();
const childTagList = ref([]);

const getTags = async () => {
  try {
    const res = await spaceTagsApi(route.query.spaceId)
    tagList.value = [{tagName: '全部', id: '-1'}, ...(res.data || [])]
  } catch (e) {

  }
}

const handleClick = async (item) => {
  const _index = checkedTagIdList.value.findIndex((d) => d === item.id);
  if (_index > -1) {
    checkedTagIdList.value.splice(_index, 1);
  } else {
    checkedTagIdList.value = [item.id];
  }

  await listRef.value?.getList({ tagIdList: checkedTagIdList.value.filter(d => d !== '-1'), pageNum: 1 });
};

const getSpecial = async () => {
  const res = await documentSpecialApi()
  currentSpecial.value = res?.data?.[0]
}

const handleToSpecial = () => {
  router.push({
    path: '/special',
    query: {
      specialId: currentSpecial.value.specialId,
      specialName: currentSpecial.value.specialName,
    }
  })
}

const handleToAI = () => {
  window.open('https://base-links.genscigroup.com/chat/share?shareId=qkztykdcqky1bcvzhexq80bj', '_blank')
}

onMounted(() => {
  // getTags();
  getSpecial();
  userScreen.getNodeList(route.query.spaceId);
  listRef.value?.getList({ tagIdList: [] })
});
</script>
<style scoped lang="scss">
.checked {
  color: #FFB537 !important;
}

.breadcrumbs > ul > li + *:before,
.breadcrumbs > ol > li + *:before {
  color: #fff !important;
}
</style>
