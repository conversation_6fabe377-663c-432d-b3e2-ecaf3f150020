<template>
  <div class="flex gap-[4px] flex-wrap items-center">
    <template :key="tag.id" v-for="tag in props.data || []">
<!--      <div class="px-[4px] py-[2px] bg-[#F2F3F5] text-[#65676B] text-[12px] rounded-[2px]" v-if="tag.highlightTag" v-html="tag.highlightTag"/>-->
      <div
          class="px-[4px] py-[2px] bg-[#F2F3F5] text-[#65676B] text-[12px] rounded-[2px]"
      >
        # {{ tag.tagName }}
      </div>
    </template>
  </div>
</template>
<script setup>
import { useScreenStore } from '@/stores/screen';
const userScreen = useScreenStore();

const props = defineProps({
  data: {
    type: Array,
    default: []
  },
  showTagColor: {
    type: Boolean,
    default: false
  },
  parentTagName: {
    type: String,
    default: ''
  }
});

const getParentTag = (tagName) => {
  return userScreen.nodeList.find(t => t.tagName === tagName) || {};
}

const styleMap = {
  '金赛增说明书': {
    color: '#fff',
    bg: '#4080FF'
  },
  '临床研究': {
    color: '#fff',
    bg: '#FF9A2E'
  },
  '优秀病例': {
    color: '#fff',
    bg: '#F76560'
  },
  '相关文献': {
    color: '#fff',
    bg: '#37D4CF'
  },
  '综合PPT': {
    color: '#fff',
    bg: '#F9CC45'
  },
  '金赛增竞品': {
    color: '#fff',
    bg: '#57A9FB'
  },
  '': {
    color: '#FF8800',
    bg: '#FFF3E6'
  },
}
</script>
<style>
em {
  color: #FFB537 !important;
  font-style: normal;
}
</style>
