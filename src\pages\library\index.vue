<template>
  <div class="mb-[16px]">
    <Search @search="search" />
  </div>
  <div class="w-[1200px] mx-auto pb-[18px]" style="min-height: calc(100vh - 421px)">
    <List ref="listRef" :showIndex="true" :hideTotal="true" :showScore="true" />
  </div>
  <Footer />
</template>
<script setup>
import { ref } from 'vue';
import Search from './components/Search.vue';
import List from './components/List.vue';
import Footer from '@/components/Footer.vue';

const listRef = ref();
const search = async (data) => {
  listRef.value?.getList(data);
}


</script>
