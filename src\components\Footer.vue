<template>
  <div class="bg-[#2A2A32] w-full h-[120px] bottom-0 z-[9999]">
    <div class="relative z-10">
<!--      <div class="w-[1440px] mx-auto px-[24px]">-->
<!--        <div class="flex justify-between">-->
<!--          <template v-for="tag in userScreen.nodeList" :key="tag.id">-->
<!--            <div class="my-[12px] text-[#fff] text-[14px] h-[40px] leading-[40px]">{{ tag.tagName }}</div>-->
<!--          </template>-->
<!--        </div>-->
<!--      </div>-->
      <div class="line"></div>
    </div>
    <div class="line-shadow"></div>
    <div class="mx-auto w-[1440px] px-[24px]">
      <div class=" flex justify-between items-center pb-[40px] pt-[24px]">
        <img :src="logo" class="w-[142px] h-[32px]">
        <div class="text-[14px] h-[20px] leading-[20px] text-[#fff] opacity-80">
          全新知识库已上线，结合AI功能为你提供专业的学习系统，全面升级知识沉淀体验
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import logo from '@/assets/images/logo.png';
// import { useScreenStore } from '@/stores/screen';
// const userScreen = useScreenStore();

</script>
<style scoped>
.line {
  height: 4px;
  background: linear-gradient(90deg, #3E1DFF 36.83%, #8E4BFF 44.6%, #D94EEA 50.53%, #EE518F 58.56%, #FFCA8B 63.17%);
}
.line-shadow {
  height: 10px;
  background: linear-gradient(90deg, #3E1DFF 36.83%, #8E4BFF 44.6%, #D94EEA 50.53%, #EE518F 58.56%, #FFCA8B 63.17%);
  filter: blur(30px);
}
</style>

