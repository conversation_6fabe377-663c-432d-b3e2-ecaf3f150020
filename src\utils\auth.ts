import { useStorage } from "@vueuse/core";

const TokenKey = 'Admin-Token';
const SuperTokenKey = 'Super-Admin-Token';

const tokenStorage = useStorage<null | string>(TokenKey, null);
const superTokenStorage = useStorage<null | string>(SuperTokenKey, null);

export const getToken = () => tokenStorage.value;
export const getSuperToken = () => superTokenStorage.value;

// eslint-disable-next-line camelcase
export const setToken = (access_token: string) => (tokenStorage.value = access_token);
export const setSuperToken = (access_token: string) => (superTokenStorage.value = access_token);

export const removeToken = () => (tokenStorage.value = null);
export const removeSuperToken = () => (superTokenStorage.value = null);
