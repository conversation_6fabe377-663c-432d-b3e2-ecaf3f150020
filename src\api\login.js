import request from '@/utils/request';
// pc端固定客户端授权id
const clientId = 'e5cd7e4891bf95d1d19206ce24a7b32e';
// 登陆tenantId租户id
const tenantId = '000080';


/**
 * @param data {LoginData}
 * @returns
 */
export function login(data) {
  const params = {
    ...data,
    clientId: data.clientId || clientId,
    grantType: data.grantType || 'password',
    tenantId
  };
  console.log(params, '-登陆');
  return request({
    url: '/auth/login',
    headers: {
      isToken: false,
      isEncrypt: true
    },
    method: 'post',
    data: params
  });
}

// 注册方法
export function register(data) {
  return request({
    url: '/auth/register',
    headers: {
      isToken: false,
      isEncrypt: true
    },
    method: 'post',
    data: data
  });
}

/**
 * 注销
 */
export function logout() {
  return request({
    url: '/auth/logout',
    method: 'post'
  });
}

/**
 * 获取验证码
 */
export function getCodeImg() {
  return request({
    url: '/auth/code',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  });
}

/**
 * 第三方登录
 */
export function callback(data) {
  const LoginData = {
    ...data,
    tenantId
    // clientId: clientId,
    // grantType: 'social'
  };
  return request({
    url: '/auth/social/callback',
    headers: {
      isToken: false
    },
    method: 'post',
    data: LoginData
  });
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/plt/user/getInfo',
    method: 'get'
  });
}

// 飞书授权登录
export function appletLoginApi(data) {
  return request({
    url: '/auth/social/applet/login',
    headers: {
      isToken: false
    },
    method: 'post',
    data
  });
}
