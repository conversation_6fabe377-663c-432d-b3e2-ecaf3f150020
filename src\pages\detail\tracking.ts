declare global {
  interface Window {
    collectEvent: {
      (eventName: string, eventData: object): void;
      (beconEvent: 'beconEvent', eventName: string, eventData: object): void; // 函数重载，beconEvent会将埋点通过浏览器的特性sendbeacon来发送，尽可能补偿数据上报，用于页面跳转前上报埋点。
    }
  }
}

interface FileInfo {
  delFlag: boolean | null;
  highlightSummary: string | null;
  highlightTitle: string | null;
  id: string;
  spaceId: string;
  isCollect: boolean;
  isLike: boolean;
  operationTime: string | null;
  ossId: string;
  ownerName: string | null;
  parentTagVoList: any[] | null;
  status: string | null;
  summary: string;
  title: string;
  type: string | null;
  url: string;
  wikiTagVoList: any[] | null;
}

export const trackArticleView = {
  startTime: 0,
  articleId: '',

  // 开始阅读文章
  start(fileInfo: FileInfo) {
    this.startTime = Date.now();
    this.articleId = fileInfo.id;
    const trackData = {
      Wiki_article_id: fileInfo.id,
      Wiki_article_title: fileInfo.title,
      Wiki_article_summary: fileInfo.summary,
      Wiki_article_oss_id: fileInfo.ossId,
      Wiki_article_url: fileInfo.url,
      Wiki_article_is_collect: fileInfo.isCollect,
      Wiki_article_is_like: fileInfo.isLike,
      Wiki_article_open_time: this.startTime,
      Wiki_article_space_id: fileInfo.spaceId,
      ...(fileInfo.ownerName && { Wiki_article_owner_name: fileInfo.ownerName }),
      ...(fileInfo.type && { Wiki_article_file_type: fileInfo.type }),
      ...(fileInfo.status && { Wiki_article_status: fileInfo.status }),
      ...(fileInfo.delFlag && { Wiki_article_del_flag: fileInfo.delFlag }),
      ...(fileInfo.operationTime && { Wiki_article_operation_time: fileInfo.operationTime }),
      ...(fileInfo.highlightTitle && { Wiki_article_highlight_title: fileInfo.highlightTitle }),
      ...(fileInfo.highlightSummary && { Wiki_article_highlight_summary: fileInfo.highlightSummary }),
      ...(fileInfo.parentTagVoList && { Wiki_article_parent_tag_vo_list: fileInfo.parentTagVoList }),
      ...(fileInfo.wikiTagVoList && { Wiki_article_wiki_tag_vo_list_raw: JSON.stringify(fileInfo.wikiTagVoList) }),
      ...(fileInfo.wikiTagVoList && { Wiki_article_wiki_tag_vo_list: fileInfo.wikiTagVoList.map(tag => tag.tagName)})
    };
    // console.log('上报文章阅读事件，传入信息', fileInfo);
    // console.log('上报文章阅读事件，携带信息', trackData);
    window.collectEvent('article_view_start', trackData);
  },

  // TODO: 文章是否处于前台，如果不在前台，是否需要暂停计时
  // 结束阅读文章
  end() {
    if (!this.startTime) return;

    const duration = Date.now() - this.startTime;
    window.collectEvent('beconEvent', 'article_view_end', {
      Wiki_article_id: this.articleId,
      Wiki_article_duration_ms: duration,
      Wiki_article_end_time: Date.now()
    });

    this.reset();
  },

  reset() {
    this.startTime = 0;
    this.articleId = '';
  }
};
