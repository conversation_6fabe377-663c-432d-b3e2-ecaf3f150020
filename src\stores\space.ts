// @ts-nocheck
import {acceptHMRUpdate, defineStore} from "pinia";
import { ref } from 'vue';
import to from 'await-to-js';
import { spaceListAPi } from '@/api/wiki';

export const useSpaceStore = defineStore('space', () => {
    const list = ref<any>([]);
    const canDownloadArr = ref(['科普专栏']);

    const getList = async (spaceId: string): Promise<void> => {
        const [err, res]: [any, any] = await to(spaceListAPi());
        if (res) {
            list.value = res.data;
            return Promise.resolve();
        }
        return Promise.reject(err);
    };

    const canDownload = (spaceName) => {
        return canDownloadArr.value.includes(spaceName)
    }

    return {
        list,
        canDownloadArr,
        canDownload,
        getList
    };
}, {
    persist: true
})

if (import.meta.hot)
    import.meta.hot.accept(acceptHMRUpdate(useSpaceStore, import.meta.hot));

export default useSpaceStore;
