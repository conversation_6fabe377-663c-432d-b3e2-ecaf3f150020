/* 下载申请确认弹框样式 */
.download-apply-confirm {
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.download-apply-confirm .el-message-box__header {
  padding: 24px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.download-apply-confirm .el-message-box__title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.download-apply-confirm .el-message-box__content {
  padding: 20px 24px;
}

.download-apply-confirm .el-message-box__message {
  font-size: 14px;
  line-height: 1.6;
  color: #4b5563;
  margin: 0;
}

.download-apply-confirm .el-message-box__btns {
  padding: 16px 24px 24px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.download-apply-confirm .el-button {
  padding: 8px 20px;
  border-radius: 6px;
  font-weight: 500;
}

.download-apply-confirm .el-button--primary {
  background-color: #1677ff;
  border-color: #1677ff;
}

.download-apply-confirm .el-button--primary:hover {
  background-color: #4096ff;
  border-color: #4096ff;
}

.download-apply-confirm .el-button--default {
  color: #6b7280;
  border-color: #d1d5db;
}

.download-apply-confirm .el-button--default:hover {
  color: #374151;
  border-color: #9ca3af;
  background-color: #f9fafb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .download-apply-confirm {
    margin: 20px;
    width: calc(100% - 40px) !important;
  }
  
  .download-apply-confirm .el-message-box__btns {
    flex-direction: column-reverse;
  }
  
  .download-apply-confirm .el-button {
    width: 100%;
    margin: 0;
  }
}
