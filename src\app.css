body {
    background-color: #F2F3F5;
    min-height: 100vh;
    min-width: 1440px;

    --primary-color: #FFB537;
    --text-color-text-1: #17191F;
    --text-color-text-2: #65676B;
    --text-color-text-3: #B6B7BA
}
html {
    background-color: #F2F3F5;
}
.breadcrumbs > ul > li + *:before, .breadcrumbs > ol > li + *:before {
  content: "/";
    color: #C6CAD1 !important;
  margin-left: 0;
  margin-right: 0;
  display: block;
   height: auto;
   width: 24px;
  padding: 0 4px;
   --tw-rotate: 0;
   transform: none;
   opacity: 1;
   border-top: 0;
   border-right: 0;
  background-color: transparent;
  text-align: center;
}

:root {
    --el-color-primary: #FFB537;
}

input::placeholder {
    color: #B6B7BA !important
}

[type='text']:focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {
    outline: none;
    outline-offset: 0;
    --tw-ring-inset: 0;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: none;
    --tw-ring-color: none;
    --tw-ring-offset-shadow: none;
    --tw-ring-shadow: none;
    box-shadow: none;
    border: none;
}

.el-pager li {
    background: none;
}

.el-pagination button.is-disabled, .el-pagination button:disabled {
    background: none;
}

.el-pagination button {
    background: none;
}
