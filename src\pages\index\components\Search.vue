<template>
  <div>
    <div class="flex">
      <div class="text-[#17191F] text-[28px]">Hi~{{ userStore.nickname }}，</div>
      <div class="text-[#17191F] text-[28px] gradient-text">前路虽长，犹可期待！</div>
    </div>
    <div class="text-[#17191F] text-[28px] ">和GenWiki一起成长</div>
  </div>
  <div>
    <div class="pt-[16px] pb-[20px] relative">
      <input
          placeholder="搜索海量内容，从这里开始"
          class="w-full text-[16px] h-[60px] pl-[16px] pr-[60px] rounded-[16px] border border-[1px] border-solid border-[#17191F]"
          v-model="value"
          @keyup.enter="search"
          maxlength="100"
      />
      <el-icon class="absolute right-[20px] top-[32px] cursor-pointer" size="26" @click="search"><Search /></el-icon>
      <img :src="ipLogo" class="w-[158px] h-[110px] absolute right-[20px] top-[-94px]" />
    </div>
<!--    <div class="flex gap-[16px]">-->
<!--      <div class="px-[8px] py-[3px] bg-[#F3F4F5] text-[#1D212B] text-[14px] font-500 border-solid border-1 border-[#E5E6EB]">长效观念</div>-->
<!--      <div class="px-[8px] py-[3px] bg-[#F3F4F5] text-[#1D212B] text-[14px] font-500">长效观念</div>-->
<!--      <div class="px-[8px] py-[3px] bg-[#F3F4F5] text-[#1D212B] text-[14px] font-500">长效观念</div>-->
<!--      <div class="px-[8px] py-[3px] bg-[#F3F4F5] text-[#1D212B] text-[14px] font-500">长效观念</div>-->
<!--    </div>-->
<!--    <div class="flex justify-center">-->
<!--      <img :src="aiSearchImg" class="w-[215px] h-[32px] cursor-pointer" @click="handleToAI()"/>-->
<!--    </div>-->
  </div>
</template>
<script setup>
import { ref } from 'vue';
import { useRouter } from "vue-router";
import { Search } from '@element-plus/icons-vue';
import { useUserStore } from '@/stores/user';
import ipLogo from '@/assets/images/ip.png';
import aiSearchImg from '@/assets/images/ai_search.png';
import {ElMessage} from "element-plus";

const userStore = useUserStore();
const router = useRouter();

const value = ref('');

const search = () => {
  // 当搜索框无内容时，不跳转到搜索结果页
  if (!value.value) {
    // ElMessage.error('请输入搜索内容')
    return
  }
  router.push({
    path: '/library',
    query: {
      keyWord: value.value, pageSize: 10,
      pageNum: 1
    }
  })
}

const handleToAI = () => {
  // ElMessage.error('暂未开放，敬请期待~')
  window.open('https://base-links.genscigroup.com/chat/share?shareId=qkztykdcqky1bcvzhexq80bj', '_blank')
}
</script>
<style>
.gradient-text {
  /* 设置文本的颜色为白色，以便于背景渐变的颜色遮罩 */
  color: white;
  /* 设置文本的背景为线性渐变 */
  /*background: linear-gradient(45deg, blue, red);*/
  background: linear-gradient(90deg, #3E1DFF 0%, #8E4BFF 29%, #D94EEA 52%, #EE518F 82%, #FFCA8B 100%);
  /* 使用 blend-mode 实现文本颜色与背景渐变颜色的混合 */
  -webkit-background-clip: text;
  background-clip: text;
  /* 使用 color 作为遮罩，实际看到的是背景渐变 */
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
  /* 防止文本阴影遮挡背景渐变 */
  -webkit-box-decoration-break: clone;
  box-decoration-break: clone;
}
</style>
