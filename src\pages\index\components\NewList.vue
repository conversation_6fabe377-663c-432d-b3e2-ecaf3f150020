<template>
  <div v-if="noAuth" class="rounded-[16px] bg-white px-[16px] overflow-hidden h-[124px] text-center">
    <img :src="noAuthImg" class="w-[90px] h-[90px] mx-auto">
    <div class="text-[#C9CDD4] text-[14px]">暂无权限</div>
  </div>
  <div v-else class="rounded-[16px] bg-white px-[16px] divide-y divide-[#E5E6EB] overflow-hidden h-[124px]">
    <div class="scroll-container  divide-y divide-[#E5E6EB] " :style="{ transform: `translateY(${-currentIndex * 42}px)` }">
    <div class="px-[20px] py-[10px]" v-for="doc in displayList" :key="doc.id">
      <div class="flex justify-between items-center gap-[8px]">
        <img :src="hornImg" class="w-[16px] h-[16px]" />
        <div class="">
          <img :src="newTagIcon" class="w-[42px] h-[20px]"/>
        </div>
        <div class="flex-1 text-[14px] text-[#65676B] flex items-center cursor-pointer" @click="handleToDetail(doc)">
          <span class="text-[--primary-color] max-w-[600px] truncate">《{{ doc.title }}》</span>
          <span>{{ doc.spaceName }}上新啦</span>
        </div>
        <div class="text-[14px] text-[#B6B7BA]">{{ dayjs(doc.operationTime).format('MM') }}/{{ dayjs(doc.operationTime).format('DD') }}</div>
      </div>
    </div>
    </div>
  </div>
</template>
<script setup>
import { ref,  onUnmounted, onMounted } from 'vue';
import dayjs from "dayjs";
import newTagIcon from '@/assets/images/new_tag_icon.png';
import hornImg from '@/assets/images/horn.png';
import {newDocListApi, spaceListAPi} from '@/api/wiki';
import {useRouter} from "vue-router";
const router = useRouter();
import noAuthImg from '@/assets/images/no-auth.png';

const data = ref([]);
const noAuth = ref(false);
const currentIndex = ref(0);

const displayList = ref([]);
const scrollTimer = ref(null);

const getList = async () => {
  try {
    const res = await newDocListApi();
    data.value = res?.data || [];
    // 复制前两条数据到列表末尾
    if(data.value.length >= 6) {
      displayList.value = [...data.value, ...data.value.slice(0, 3)];
    }else {
      displayList.value = data.value
    }
    if (data.value.length > 3) {
      startScroll();
    }
    // 初始展示
    // displayData();
  } catch (e) {

  }
}

const startScroll = () => {
  scrollTimer.value = setInterval(() => {
    currentIndex.value = currentIndex.value + 3;
    // 当滚动到倒数第二条时
    if (currentIndex.value >= data.value.length) {
      // 等待动画结束
      setTimeout(() => {
        // 暂停过渡动画
        document.querySelector('.scroll-container').style.transition = 'none';
        // 重置到开头位置
        currentIndex.value = 0;
        // 等待0.5s恢复过渡动画
        setTimeout(() => {
          document.querySelector('.scroll-container').style.transition = 'all 0.5s linear';
        }, 500);
      }, 500);
    }
  }, 5000);
}

const handleToDetail = (row) => {
  router.push({
    path: '/detail',
    query: {
      id: row.id,
      ossId: row.ossId
    }
  })
}

const getSpaceList = async () => {
  try {
    const res = await spaceListAPi({
      isAdmin: false
    });
    const data = res?.data?.filter(d => d.isVisible);
    if(!data?.length) {
      noAuth.value = true
    }
  } catch (e) {

  }
}

onMounted(() => {
  getList()
  getSpaceList()
})
onUnmounted(() => {
  // 清除所有定时器
  if (scrollTimer.value) clearInterval(scrollTimer.value);
});
</script>
<style scoped>
.scroll-container {
  transition: all 0.5s linear;
}
</style>
