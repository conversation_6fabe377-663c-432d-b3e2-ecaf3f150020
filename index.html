<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>GenWiKi</title>
  </head>
  <body>
    <div id="app" class="flex flex-col min-h-0 mx-auto"></div>
    <script type="module" src="/src/main.ts"></script>
    <noscript>
        GenWiKi
    </noscript>
  </body>
  <script>
    // 集成（火山引擎增长分析/埋点平台）增长营销套件SDK
    (function(win, export_obj) {
      win['LogAnalyticsObject'] = export_obj;
      if (!win[export_obj]) {
        function _collect() {
          _collect.q.push(arguments);
        }
        _collect.q = _collect.q || [];
        win[export_obj] = _collect;
      }
      win[export_obj].l = +new Date();
    })(window, 'collectEvent');

    window.collectEvent('init', {
      app_id: 10000060, // 参考2.1节获取，注意类型是number而非字符串
      channel_domain: 'https://snssdk.genscigroup.com', // 设置私有化部署数据上送地址，参考2.2节获取
      log: true, // true:开启日志，false:关闭日志
      autotrack: true, // 全埋点开关，true开启，false关闭
      enable_stay_duration: true,
      enable_debug: true,
      spa: true
    });
    // 此处可添加设置uuid、设置公共属性等代码
    const env = (window.location.host === 'genwiki.dgtmeta.com' ? 'production' : 'development');
    // 设置自定义的公共属性
    window.collectEvent('config', {
      Wiki_env: env
      // Wiki_env: 'development'
    });
    window.collectEvent('start'); // 通知SDK设置完毕，可以真正开始发送事件了
  </script>
  <script async src="https://datarangers.genscigroup.com/minio.byterangers.onpremise.docor.static/collect-privity-v5.1.6.js"></script>
</html>
