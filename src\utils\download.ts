import { degrees, PDFDocument, rgb } from "pdf-lib";
// @ts-ignore
import fontkit from "@pdf-lib/fontkit";
import download from "downloadjs";
// @ts-ignore
import {
  docLogOperateApi,
  ossFileInfoApi,
  getApprovalStatusApi,
  downloadApplyApi,
} from "@/api/wiki";
// @ts-ignore
import useUserStore from "@/stores/user";
const userStore = useUserStore();
// @ts-ignore
import { documentDetailApi } from "@/api/wiki";
import { LoadingInstance } from "element-plus/es/components/loading/src/loading";
import { ElLoading, ElMessage } from "element-plus";

let downloadLoadingInstance: LoadingInstance;

// 检查下载权限和处理下载逻辑
const checkDownloadPermission = async (fileInfo) => {
  const { downloadType } = fileInfo;

  // 1 - 禁止下载
  if (downloadType === "1") {
    ElMessage.warning("该文件禁止下载");
    return false;
  }

  // 3 - 直接下载
  if (downloadType === "3") {
    return true;
  }

  // 2 - 审批流下载
  if (downloadType === "2") {
    try {
      const { code, data } = await getApprovalStatusApi(fileInfo.id);
      if (code !== 200) {
        ElMessage.error("获取审批状态失败");
        return false;
      }

      const { hasApprovalFlow, approvalStatus } = data;

      // 如果没有审批流，需要先申请
      if (!hasApprovalFlow) {
        try {
          const applyRes = await downloadApplyApi({
            docId: fileInfo.id,
            spaceId: fileInfo.spaceId,
          });
          if (applyRes.code === 200) {
            ElMessage.success("下载申请已提交，请等待审批");
          } else {
            ElMessage.error("下载申请提交失败");
          }
        } catch (error) {
          ElMessage.error("下载申请提交失败");
        }
        return false;
      }

      // 如果有审批流，检查审批状态
      switch (approvalStatus) {
        case "APPROVED":
        case "approved":
          return true;
        case "PENDING":
        case "pending":
          ElMessage.info("下载申请审批中，请耐心等待");
          return false;
        case "REJECTED":
        case "rejected":
          ElMessage.warning("下载申请已被拒绝");
          return false;
        default:
          ElMessage.warning("审批状态异常，请联系管理员");
          return false;
      }
    } catch (error) {
      ElMessage.error("获取审批状态失败");
      return false;
    }
  }

  // 默认情况
  ElMessage.warning("下载类型未知");
  return false;
};

// 执行实际下载
const performDownload = async (fileInfo, ossFile) => {
  const logInitParams = {
    content: fileInfo?.title,
    systemTerminal: "PC",
    spaceName: fileInfo?.spaceName,
    documentId: fileInfo?.id,
  };

  const { fileSuffix = "", url = "" } = ossFile;

  if (fileSuffix === ".pdf" || fileSuffix === ".PDF") {
    downloadLoadingInstance = ElLoading.service({
      text: "正在下载数据，请稍候",
      background: "rgba(0, 0, 0, 0.7)",
    });

    try {
      const existingPdfBytes = await fetch(url).then((res) =>
        res.arrayBuffer()
      );
      const pdfDoc = await PDFDocument.load(existingPdfBytes);
      const pages = pdfDoc.getPages();

      // 设置水印参数
      pdfDoc.registerFontkit(fontkit);
      const fontBytes = await fetch("./fonts/STXIHEI.TTF").then((res) =>
        res.arrayBuffer()
      );
      const font = await pdfDoc.embedFont(fontBytes);
      const watermarkText = `${userStore.nickName} - ${userStore.userName}`;
      const fontSize = 14;
      const opacity = 0.4;
      const angle = 45; // 旋转角度

      // 为每一页添加水印
      pages.forEach((page) => {
        for (let y = 0; y < 4; y++) {
          for (let x = 0; x < 3; x++) {
            page.drawText(watermarkText, {
              x: x * 130 + 100,
              y: y * 200,
              size: fontSize,
              color: rgb(0.8, 0.8, 0.8),
              opacity: opacity,
              rotate: degrees(angle),
              font,
            });
          }
        }
      });

      const pdfBytes = await pdfDoc.save();
      // @ts-ignore
      download(pdfBytes, ossFile.originalName, "application/pdf");

      await docLogOperateApi({
        logType: "download",
        ...logInitParams,
      });
    } catch (e) {
      console.log(e, "下载文件失败");
      ElMessage.error("下载文件失败");
    } finally {
      downloadLoadingInstance.close();
    }
  } else {
    // @ts-ignore
    await window?.__$download.oss(fileInfo.ossId);
    await docLogOperateApi({
      logType: "download",
      ...logInitParams,
    });
  }
};

const handleDownload = async (file, event) => {
  event?.stopPropagation();

  const { code, data, msg } = await documentDetailApi(file.id);
  if (code !== 200) return false;

  const fileInfo = data || {};

  // 检查下载权限
  const canDownload = await checkDownloadPermission(fileInfo);
  if (!canDownload) {
    return false;
  }

  // 获取OSS文件信息
  const ossFileRes = await ossFileInfoApi(data.ossId);
  const ossFile = ossFileRes?.data?.rows?.[0];

  if (!ossFile) {
    ElMessage.error("文件信息获取失败");
    return false;
  }

  // 执行下载
  await performDownload(fileInfo, ossFile);
};

export { handleDownload };
