import {degrees, PDFDocument, rgb} from "pdf-lib";
// @ts-ignore
import fontkit from "@pdf-lib/fontkit";
import download from "downloadjs";
// @ts-ignore
import {docLogOperateApi, ossFileInfoApi} from "@/api/wiki";
// @ts-ignore
import useUserStore from "@/stores/user";
const userStore = useUserStore();
// @ts-ignore
import {documentDetailApi} from "@/api/wiki";
import {LoadingInstance} from "element-plus/es/components/loading/src/loading";
import {ElLoading} from "element-plus";

let downloadLoadingInstance: LoadingInstance;

const handleDownload = async (file, event) => {
  event?.stopPropagation();

  const { code, data, msg } = await documentDetailApi(file.id)
  if(code !== 200) return false;
  const fileInfo = data || {}
  const logInitParams = {
    content: fileInfo?.title,
    systemTerminal: 'PC',
    spaceName: fileInfo?.spaceName,
    documentId: fileInfo?.id
  }

  const ossFileRes = await ossFileInfoApi(data.ossId);
  const ossFile = ossFileRes?.data?.rows?.[0];
  const { fileSuffix = '', url = '' } = ossFile;
  if(fileSuffix === '.pdf' || fileSuffix === '.PDF') {

    downloadLoadingInstance = ElLoading.service({ text: '正在下载数据，请稍候', background: 'rgba(0, 0, 0, 0.7)' });

    try {
      const existingPdfBytes = await fetch(url).then(res => res.arrayBuffer())

      const pdfDoc = await PDFDocument.load(existingPdfBytes)

      const pages = pdfDoc.getPages()

      // 设置水印参数
      pdfDoc.registerFontkit(fontkit);
      const fontBytes = await fetch('./fonts/STXIHEI.TTF').then(res => res.arrayBuffer());
      const font = await pdfDoc.embedFont(fontBytes);
      const watermarkText = `${userStore.nickName} - ${userStore.userName}`;
      const fontSize = 14;
      const opacity = 0.4;
      const angle = 45; // 旋转角度

      // 为每一页添加水印
      pages.forEach(page => {
        for (let y = 0; y < 4; y++) {
          for (let x = 0; x < 3; x++) {
            page.drawText(watermarkText, {
              x: x * 130 + 100,
              y: y * 200,
              size: fontSize,
              color: rgb(0.8, 0.8, 0.8),
              opacity: opacity,
              rotate: degrees(angle),
              font
            });
          }
        }
      });

      const pdfBytes = await pdfDoc.save()

      // @ts-ignore
      download(pdfBytes, ossFile.originalName, "application/pdf");
      await docLogOperateApi({
        logType: 'download',
        ...logInitParams
      })
    } catch (e) {
      downloadLoadingInstance.close();
      console.log(e, '下载文件失败')
    } finally {
      downloadLoadingInstance.close();
    }
  }else {
    // @ts-ignore
    // await proxy?.$download.oss(file.ossId);
    await window?.__$download.oss(file.ossId);
    await docLogOperateApi({
      logType: 'download',
      ...logInitParams
    })
  }

}

export {
  handleDownload
}
