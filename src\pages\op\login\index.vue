<template>
  <div class="skeleton" v-if="feishuLoading">
    <div id="loader"></div>
    <div class="skeleton-item" v-for="item in 9" :class="{'first':item===1}" :key="item"></div>
  </div>
  <div class="login" v-else>
    <div class="login-content">
      <!-- </div> -->
      <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form-new">
        <el-form-item prop="username">
          <el-input v-model="loginForm.username" type="text" size="large" auto-complete="off" placeholder="账号">
            <!-- <input :value="loginForm.username" /> -->
            <!-- <template #prefix><svg-icon icon-class="user" class="el-input__icon input-icon" /></template> -->
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input v-model="loginForm.password" type="password" size="large" auto-complete="off" placeholder="密码" @keyup.enter="handleLogin">
            <!-- <template #prefix><svg-icon icon-class="password" class="el-input__icon input-icon" /></template> -->
          </el-input>
        </el-form-item>
        <el-button :loading="loading" size="large" type="primary" style="width:100% ;margin-top:40px; color: #FFFFFF;" @click.prevent="handleLogin">
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
        <div class="banck-feishu-btn" @click="doFeishuLogin()">
          <span>飞书登录</span>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';
import { ref } from 'vue';
import { useUserStore } from '@/stores/user';
import { to } from 'await-to-js';
import logo from '@/assets/images/logo.png'
const userStore = useUserStore();
const router = useRouter();

const loginForm = ref({
  tenantId: '000080',
  username: '000080',
  password: 'wF4tc2Vfrwe5',
  rememberMe: false,
  code: '',
  uuid: ''
});
const loading = ref(false);
const loginRef = ref();

const loginRules = {
  tenantId: [{ required: true, trigger: "blur", message: "请输入您的租户编号" }],
  username: [{ required: true, trigger: 'blur', message: '请输入您的账号' }],
  password: [{ required: true, trigger: 'blur', message: '请输入您的密码' }],
  code: [{ required: true, trigger: 'change', message: '请输入验证码' }]
};


const handleLogin = () => {
  loginRef.value?.validate(async (valid, fields) => {
    if (valid) {
      loading.value = true;
      // 调用action的登录方法
      const [err] = await to(userStore.adminLogin(loginForm.value));
      if (!err) {
        await router.push({ path: '/op/index' });
        loading.value = false;
      } else {
        loading.value = false;
      }
    } else {
      console.log('error submit!', fields);
    }
  });
};

</script>

<style lang="scss" scoped>
.login {
  display: flex;
  padding-left:107px;
  background-image: url("@/assets/images/loginBg.png");
  background-size: cover;
  height: calc(100vh - 64px);
}
.login-content {
  display:flex;
  flex-direction:column;
  align-items:center;
  justify-content: center;
  // margin-top: 288px;
  padding:24px 0;
  .login-logo {
    height:40px;
    padding-left: 40px;
  }
  .feishu-btn {
    width:327px;
    height:46px;
    margin-top:151px;
    margin-bottom:20px;
    .login-icon {
      width:20px;
      height:20px;
      margin-right:4px;
    }
  }
}
.login-form-new {
 width:375px;
 margin-top:80px;
 padding:0 24px;
 ::v-deep {
  .el-input__wrapper {
    // background:#FFFFFF !important;
    width:275px!important;
    // color: #BFBFBF;
  }

  .el-input {
    --el-input-border:#FFFFFF !important;
  }
  .el-button{
    --el-button-hover-text-color:#2551F2;
    --el-button-border-color: #2551F2 !important;
    --el-button-hover-border-color: #2551F2;
  }
 }
 .banck-feishu-btn {
  width:100% ;
  margin:20px 0 0 0;
  text-align: center;
  font-size: 14px;
  height: 40px;
  line-height: 40px;
  color: #2551F2;
  border-radius: 4px;
  border: 1px solid #2551F2;
  &:hover{
  color: #5174F5;
  border: 1px solid #5174F5;
  }
 }

}

.skeleton {
  padding: 16px;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.skeleton-item {
  height: 30px;
  margin: 0 30px;
  margin-top: 30px;
  border-radius: 14px;
  background: #F3F4F5;
  background-size: 400% 100%;
  -webkit-transition: all 0.3s 1s ease-out;
  transition: all 0.3s 1s ease-out;
}
.skeleton-item:first-child{
  width: 30%;
}
.skeleton-item:last-child {
  width: 50%;
}
</style>
