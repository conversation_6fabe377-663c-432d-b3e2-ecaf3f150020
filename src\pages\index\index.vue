<template>
  <div>
    <div class="flex justify-center gap-[24px] p-[23px]">
      <div class="w-[1088px]">
        <div class="header w-[1088px] rounded-[16px] px-[60px] pt-[30px] pb-[20px]">
          <Search />
        </div>
        <div class="mt-[24px]">
          <NewList />
        </div>
        <div>
          <Knowledge />
        </div>
      </div>
      <div class="w-[280px]">
        <Hot />
        <div class="h-[24px] bg-[#F2F3F5]"></div>
        <History />
      </div>
    </div>
    <div class="flex justify-center">
      <div class="w-[1392px] h-[240px] m-[0 auto]">
        <img :src="bgImg2" class="w-full h-full">
      </div>
    </div>
  </div>
</template>
<script setup>
import Search from './components/Search.vue';
import Knowledge from './components/Knowledge.vue';
import bgImg2 from '@/assets/images/bg-2.png';
import Hot from './components/Hot.vue';
import History from './components/ViewHistory.vue';
import NewList from './components/NewList.vue';
import { useScreenStore } from '@/stores/screen';
import {onBeforeMount} from "vue";
import useSpaceStore from "@/stores/space";
const userScreen = useScreenStore();
const useSpace = useSpaceStore();

onBeforeMount(() => {
  // 预加载-标签数据
  // userScreen.getNodeList();
  // useSpace.getList();
  userScreen.resetNodeList();
})

</script>
<style scoped>
  .header {
    background-image: url("@/assets/images/bg-1.png");
    background-repeat: no-repeat;
    background-size: cover;
  }
</style>
