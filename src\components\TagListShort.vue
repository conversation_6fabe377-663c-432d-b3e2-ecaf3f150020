<template>
  <div class="flex gap-[4px] flex-nowrap items-center">
    <template v-for="(tag, index) in visibleTags" :key="tag.id">
      <div class="px-[4px] py-[2px] bg-[#F2F3F5] text-[#65676B] text-[12px] rounded-[2px]">
        # {{ tag.tagName }}
      </div>
    </template>
    <div v-if="hasMoreTags"
      class="px-[4px] py-[2px] text-[12px] rounded-[2px] relative"
      @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
      <SvgIcon :width="12" :height="12" color="#C9CDD4" viewBox="0 0 12 12" class="inline-block">
        <MoreSvg />
      </SvgIcon>
      <!-- 浮层 -->
      <div v-if="showTooltip"
        class="absolute left-0 bottom-[100%] mb-[8px] bg-white shadow-lg rounded-[4px] p-[8px] z-10">
        <div v-for="tag in hiddenTags" :key="tag.id" class="whitespace-nowrap mb-[4px] last:mb-0">
          # {{ tag.tagName }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useScreenStore } from '@/stores/screen';
import { computed, ref } from 'vue';
import SvgIcon from '@/components/SvgIcon.vue';
import MoreSvg from '@/assets/images/svgIcons/more.vue';

const userScreen = useScreenStore();
const showTooltip = ref(false);

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  showTagColor: {
    type: Boolean,
    default: false
  },
  parentTagName: {
    type: String,
    default: ''
  }
});

const visibleTags = computed(() => {
  return (props.data || []).slice(0, 2);
});

const hiddenTags = computed(() => {
  return (props.data || []).slice(2);
});

const hasMoreTags = computed(() => {
  return (props.data || []).length > 2;
});

const getParentTag = (tagName) => {
  return userScreen.nodeList.find(t => t.tagName === tagName) || {};
}

const styleMap = {
  '金赛增说明书': {
    color: '#fff',
    bg: '#4080FF'
  },
  '临床研究': {
    color: '#fff',
    bg: '#FF9A2E'
  },
  '优秀病例': {
    color: '#fff',
    bg: '#F76560'
  },
  '相关文献': {
    color: '#fff',
    bg: '#37D4CF'
  },
  '综合PPT': {
    color: '#fff',
    bg: '#F9CC45'
  },
  '金赛增竞品': {
    color: '#fff',
    bg: '#57A9FB'
  },
  '': {
    color: '#FF8800',
    bg: '#FFF3E6'
  },
}
</script>
<style>
em {
  color: #FFB537 !important;
  font-style: normal;
}
</style>
