<template>
  <div class="relative">
    <div
        class="flex items-top flex-nowrap"
        v-for="item in topSearchList"
        :key="item.key"
    >
      <div
          class="px-[8px] text-[14px] h-[22px] leading-[22px] mx-4 text-[#17191F] cursor-pointer"
          style="white-space: nowrap; border: 1px solid transparent; font-weight: bold"
          :class="topKeys.includes(item.id) ? 'color' :   ''"
          @click="clickScreenParent(item.id)"
      >
        {{ item.tagName }}
      </div>
      <div class="w-[1px] h-[20px] bg-[#F2F3F5]"></div>
      <div class="flex flex-row items-center flex-wrap" style="width: 90%">
        <div
            class="px-[8px] text-[14px] leading-[22px] mx-[6px] text-[#65676B] font-400 cursor-pointer mb-[12px]"
            :class="t.active ? 'color' : ''"
            v-for="t in item.children"
            :key="t.tagName"
            @click="clickScreen(item.id, t.id)"
            style=" border: 1px solid transparent"
        >
          <div style="white-space: nowrap">
            {{ t.tagName }}
          </div>
        </div>
      </div>
    </div>

    <div class="flex justify-end mt-[16px] items-center text-[#FFB537] cursor-pointer text-[14px]" @click="reset()" ><el-icon size="20"><Refresh class="mr-[4px]" /></el-icon>重置</div>
  </div>

</template>
<script setup>
import { onMounted, ref, defineEmits } from "vue";
import { useRouter, useRoute } from 'vue-router';
import { Refresh } from '@element-plus/icons-vue';
import { useScreenStore } from '@/stores/screen';
const userScreen = useScreenStore();

const router = useRouter();
const route = useRoute();

const emit = defineEmits(["screen"]);

const topSearchList = ref([]);

const topKeys = ref([]) // 第二级id

// 递归函数来获取特定节点的值
const getNodeValue = (arr, targetValue) => {
  for (let i = 0; i < arr.length; i++) {
    const element = arr[i];

    if (element.tagName === targetValue && element.active) {
      return element.id;
    }

    const result = getNodeValue(element.children || [], targetValue);

    if (result) {
      return result;
    }
  }

  return null;
}

const getIds = () => {
  const activeIds = topSearchList.value.reduce((acc, item) => {
    const activeChildrenIds = item.children
      .filter((child) => child.active)
      .map((child) => child.id);
    return [...acc, ...activeChildrenIds];
  }, []);

  return activeIds;
};

const clickScreen = (id1, id2) => {
  const item = topSearchList.value.find((item) => item.id === id1);
  if (item) {
    const t = item.children.find((t) => t.id === id2);
    if (t) {
      t.active = !t.active;
    }
  }
  const ids = getIds();
  topSearchList.value.forEach(item => {
    if(item.children && item.children.some(d => d.active)) {
      const index = topKeys.value.indexOf(item.id);
      if(index > -1) {
        topKeys.value.splice(index, 1)
      }
    }
  })
  emit("screen", [...ids, ...topKeys.value]);
};

const clickScreenParent = (id1) => {
  topSearchList.value.forEach(item => {
    if(item.id === id1) {
      if(item.children) {
        item.children.forEach(d => {
          d.active = false
        })
      }
    }
  })
  const index = topKeys.value.indexOf(id1);
  if(index > -1) {
    topKeys.value.splice(index, 1)
  }else {
    topKeys.value.push(id1)
  }
  const ids = getIds();
  emit("screen", [...ids, ...topKeys.value]);
};

const getData = async () => {
  try {
    topSearchList.value = JSON.parse(JSON.stringify(userScreen.nodeList));
  } catch (e) {
    //
    console.log(e, 'eeeeeeeeeeee')
  }
}

const reset = async () => {
  await getData()
  topKeys.value = [];
  emit("screen", []);
}

onMounted(async () => {
  await getData();
});
</script>
<style scoped>
.color {
  color: #fff;
  background: #FFB537!important;
  border: 1PX solid transparent!important;
  border-radius: 40px;
}

.color2 {
  color: #17191F;
  background: #FFECCB;
  border: 1px dashed #FFB537;
}
.heightColor {
  background: #FFB537;
}
</style>
