<template>
  <el-dialog
      v-model="shareVisible"
      title="分享"
      width="500"
  >
    <input :value="shareText" id="share-url" style="height: 0; width: 0; overflow: hidden; opacity: 0; position: absolute" disabled />
    <div class="flex items-center">
      <div class="truncate bg-[#F3F4F5] rounded-[2px] h-[32px] leading-[32px] px-[16px] text-[#86909C]">{{ shareInfo }}</div>
      <el-button color="#2551F2" @click="copyUrl()">复制链接</el-button>
    </div>
  </el-dialog>
</template>
<script setup>

import {ref} from "vue";
import {copyText} from "@/utils";
import {ElMessage} from "element-plus";
import {docLogOperateApi} from "@/api/wiki";

const shareInfo = ref('');
const shareText = ref('');
const fileInfo = ref({});
const shareVisible = ref(false);

const copyUrl = () => {
  copyText('share-url', async () => {
    ElMessage.success('链接复制成功，快去分享给好友吧～');
    await docLogOperateApi({
      logType: 'share',
      content: fileInfo.value.title,
      systemTerminal: 'PC',
      spaceName: fileInfo.value.spaceName,
      documentId: fileInfo.value.id
    })
  })
}

const open = (row) => {
  fileInfo.value = row;
  const uri = encodeURIComponent(`${window.location.origin}/applet_login/?ossId=${fileInfo.value.ossId}&id=${fileInfo.value.id}&isShare=1`)
  shareInfo.value = `https://open.feishu.cn/open-apis/authen/v1/authorize?lk_jump_to_browser=true&app_id=${import.meta.env.VITE_APP_ID}&redirect_uri=${uri}&scope=&state=`;
  shareText.value = `您的好友给您分享了一篇文章，快来看看吧！https://open.feishu.cn/open-apis/authen/v1/authorize?lk_jump_to_browser=true&app_id=${import.meta.env.VITE_APP_ID}&redirect_uri=${uri}&scope=&state=` + ' ，复制本条信息，打开浏览器查看精彩内容！';
  shareVisible.value = true;
}

defineExpose({
  open
})

</script>
