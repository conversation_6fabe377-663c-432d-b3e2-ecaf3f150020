// 测试下载功能的示例代码
// 这个文件用于测试新的下载逻辑

import { ElMessage } from "element-plus";

// 模拟测试数据
const testFiles = [
  {
    id: 1,
    title: "测试文档.pdf",
    type: "1", // 文档类型
    downloadType: "1", // 禁止下载
    url: "https://example.com/test.pdf",
    ossId: 123,
    spaceId: 1,
  },
  {
    id: 2,
    title: "测试文档.docx",
    type: "1", // 文档类型
    downloadType: "2", // 审批流下载
    url: "https://example.com/test.docx",
    ossId: 124,
    spaceId: 1,
  },
  {
    id: 3,
    title: "测试文档.xlsx",
    type: "1", // 文档类型
    downloadType: "3", // 直接下载
    url: "https://example.com/test.xlsx",
    ossId: 125,
    spaceId: 1,
  },
  {
    id: 4,
    title: "外部链接",
    type: "2", // 链接类型
    downloadType: "3",
    url: "https://www.baidu.com",
    ossId: null,
    spaceId: 1,
  },
];

// 测试文件类型判断逻辑
export const testFileTypeHandling = (file) => {
  console.log("测试文件:", file);

  // 1. 若是文档类型，交互保持不变 type==="1"
  if (file.type === "1") {
    console.log("文档类型，跳转到详情页");
    return "detail";
  }

  // 2. 若是链接类文件，点击文件名直接跳转至原链接处
  if (
    file.url &&
    (file.url.startsWith("http://") || file.url.startsWith("https://"))
  ) {
    console.log("链接类型，直接跳转:", file.url);
    return "external";
  }

  // 3. 若是其他类型文件，则直接展示该文件本身
  console.log("其他类型，展示文件本身");
  return "display";
};

// 测试下载权限逻辑
export const testDownloadPermission = (file) => {
  console.log("测试下载权限:", file);

  const { downloadType } = file;

  // 1 - 禁止下载
  if (downloadType === "1") {
    console.log("禁止下载");
    return { canDownload: false, message: "该文件禁止下载" };
  }

  // 3 - 直接下载
  if (downloadType === "3") {
    console.log("直接下载");
    return { canDownload: true, message: "可以直接下载" };
  }

  // 2 - 审批流下载
  if (downloadType === "2") {
    console.log("审批流下载，需要检查审批状态");
    return { canDownload: false, message: "需要审批流程" };
  }

  return { canDownload: false, message: "下载类型未知" };
};

// 运行测试
export const runTests = () => {
  console.log("=== 开始测试文件处理逻辑 ===");

  testFiles.forEach((file, index) => {
    console.log(`\n--- 测试文件 ${index + 1} ---`);

    // 测试文件类型处理
    const typeResult = testFileTypeHandling(file);
    console.log("文件类型处理结果:", typeResult);

    // 测试下载权限
    const downloadResult = testDownloadPermission(file);
    console.log("下载权限检查结果:", downloadResult);
  });

  console.log("\n=== 测试完成 ===");
};

// 模拟用户信息数据
const mockUserInfo = {
  userId: "1868554249369452583",
  userName: "GS5738",
  nickName: "张浩威",
  dept: {
    deptCode: "GSZN160203",
    deptName: "后端开发部",
  },
  posts: [
    {
      postCode: "GSZN16F030",
      postName: "Web工程师",
    },
  ],
};

// 测试下载申请参数构建
export const testDownloadApplyParams = (fileInfo, userInfo = mockUserInfo) => {
  console.log("测试下载申请参数构建:", fileInfo);

  const dept = userInfo.dept || {};
  const posts = userInfo.posts || [];
  const primaryPost = posts[0] || {};

  const applyParams = {
    docId: fileInfo.id,
    spaceId: fileInfo.spaceId,
    // 申请人信息
    applicant: userInfo.nickName || userInfo.userName,
    applicantCode: userInfo.userName,
    // 岗位信息
    postCode: primaryPost.postCode || "",
    postName: primaryPost.postName || "",
    // 部门信息
    deptCode: dept.deptCode || "",
    deptName: dept.deptName || "",
  };

  console.log("构建的申请参数:", applyParams);
  return applyParams;
};

// 导出测试函数
export default {
  testFileTypeHandling,
  testDownloadPermission,
  testDownloadApplyParams,
  runTests,
  testFiles,
  mockUserInfo,
};
