<template></template>
<script setup>
import { onBeforeMount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useUserStore } from '@/stores/user';
const userStore = useUserStore();
const router = useRouter();
const route = useRoute()
const appCode = 'athena_wiki';
const tenantId = '000080';


const login = async () => {
  if(route.query.error) {
    if(route.query.error === 'access_denied') {
      localStorage.clear();
      window.location.href = '/login'
    }
  }
  if(route.query.code) {
    const params = {
      source: "FEISHU",
      code: route.query.code,
      appCode,
      tenantId
    };
    await userStore.appletLogin(params);
    if(route.query.ossId && route.query.id && route.query.isShare) {
      await router.push({
        path: `/detail`,
        query: {
          id: route.query.id,
          ossId: route.query.ossId,
          isShare: 1
        }
      })
    }else {
      await router.replace({
        path: '/'
      })
    }

  }
}

onBeforeMount(() => {
  login();
})
</script>
