<template>
  <div class="mb-[14px] mt-[36px] flex gap-[10px] items-center">
    <img :src="playImg" class="w-[24px] h-[24px]" />
    <div class="text-[20px] text-[#17191F]">正在更新</div>
  </div>
<!--  <img :src="banner" class="h-[160px] w-[1088px] cursor-pointer" @click="handleToCate('金赛增专栏')"/>-->
  <el-skeleton :rows="10" animated v-if="!spaceList.length" />
  <div v-else>
    <div v-for="(space, index) in spaceList" class="flex justify-between flex-nowrap bg-white rounded-[16px] px-[24px] py-[16px] mb-[16px] gap-[16px] items-center" @click="handleToCate(space)">
      <div>
        <div class="h-[20px] mb-[8px] flex items-center gap-[8px]">
          <img :src="NOImg" class="w-[8px] h-[16px]">
          <div>NO.{{ index + 1 }}</div>
        </div>
        <div class="text-[28px] text-[#17191F] h-[39px] leading-[39px] mb-[8px] font-bold">{{ space.spaceName }}</div>
        <div class="text-[14px] text-[#65676B] h-[47px] mb-[8px]">{{ spaceMapper?.[space.spaceName]?.desc }}</div>
        <div class="flex items-center">
          <div class="flex justify-center items-center gap-[4px] w-[114px] py-[11px] bg-[#000] rounded-[100px] cursor-pointer mr-[16px]">
            <img :src="arrowRightImg" class="w-[17px] h-[17px]">
            <div class="text-white text-[15px]">去查看</div>
          </div>
          <img :src="AvatarGroupImg" class="w-[136px] h-[32px] mr-[3px]" />
          <div v-if="!!space.userTotal" class="text-[14px] text-[#65676B] mt-[2px]">{{ space.userTotal }}位 小伙伴正在参与</div>
        </div>
      </div>
      <div>
        <img :src="spaceMapper?.[space.spaceName]?.img" class="min-w-[180px] h-[127px]">
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref } from 'vue';
import playImg from '@/assets/images/play-circle-fill.png';
import jszZhuanlanImg from '@/assets/images/jsz_zhuanlan.png';
import kpZhuanlanImg from '@/assets/images/kp_zhuanlan.png';
import arrowRightImg from '@/assets/images/arrow-right.png';
import NOImg from '@/assets/images/NO.png';
import AvatarGroupImg from '@/assets/images/avatar_group.png';
import nxjkZhuanlanImg from '@/assets/images/nxjk_zhuanlan.png';
import szjkZhuanlanImg from '@/assets/images/szjk_zhuanlan.png';

import { spaceListAPi } from '@/api/wiki';

import { useRouter } from 'vue-router';
import {onMounted} from "vue";
import {ElMessage} from "element-plus";

const router = useRouter();

const spaceList = ref([]);

const spaceMapper = {
  '金赛增专栏': {
    desc: '依托AI模型，持续沉淀金赛增最新优质内容，通过精准检索与知识推荐，为用户提供个性化内容输出。涵盖疾病诊疗、金赛增说明书、优秀病例、相关文献、以及综合PPT等。',
    img: jszZhuanlanImg
  },
  '科普专栏': {
    desc: '科普专栏涵盖儿童身高、儿童肥胖、儿童神经与心理、儿童体姿态、儿童性发育、儿童营养、中西医治疗性早熟等多个领域的内容。包含PPT、视频、展板、易拉宝以及三折页等科普活动物料。',
    img: kpZhuanlanImg
  },
  '金妍迪科-女性健康专栏': {
    desc: '深耕女性全生命周期的健康守护，涵盖达那唑栓、妇科器械、金赛欣 (妇科)、金舒安、氯喹、美适亚、仕达思 IVD、题库、通泽产品、无瘤防御相关知识。',
    img: nxjkZhuanlanImg
  },
  '金妍迪科-生殖健康专栏': {
    desc: '打造全球领先的不孕不育全线解决方案，涵盖金赛恒、金赛捷、金赛欣、曲普瑞林、赛增金赛增、实验室设备相关知识。',
    img: szjkZhuanlanImg
  }
}

const handleToCate = (item) => {
  const { spaceId, spaceName, spaceCode } = item || {};
  if(item.isVisible) {
    router.push({
      path: '/categoryDetail',
      query: {
        spaceId,
        spaceName,
        spaceCode,
        orderByColumn: 'operationTime',
        isAsc: 'desc'
      }
    })
  }else {
    ElMessage.error('暂无空间权限！')
  }

}

const getSpaceList = async () => {
  try {
    const res = await spaceListAPi({
      isAdmin: false
    });
    spaceList.value = res.data || [];
    // space.value = res?.data?.[0]
  } catch (e) {

  }
}

onMounted(() => {
  getSpaceList()
})

</script>
<style scoped>
.triangle {
  width: 0;
  height: 0;
  border-bottom: 4px solid transparent;
  border-top: 4px solid transparent;
  border-left: 5px solid #C9CDD4;
}
</style>
