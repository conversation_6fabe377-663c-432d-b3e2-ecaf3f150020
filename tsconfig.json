{
  "compilerOptions": {
    "target": "esnext",
    "module": "esnext",
    "moduleResolution": "node",
    "strict": true,
    "jsx": "preserve",
    "sourceMap": false,
    "lib": ["esnext", "dom"],
    "types": ["vite/client", "cypress", "vite-plugin-pages/client", "vite-plugin-vue-layouts/client","@intlify/vite-plugin-vue-i18n/client"],
    "resolveJsonModule": true,
    "plugins": [{ "name": "@vuedx/typescript-plugin-vue" }],
    "paths": {
      "@/*": ["./src/*"]
    },
    "allowJs": true,
    "skipLibCheck": true,
  },
  "include": [],
  "exclude": ["node_modules", "dist", "public", "tests", "**/*.ts"]
}
