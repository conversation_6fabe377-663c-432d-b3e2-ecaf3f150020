// 详情页面展示逻辑测试文件

// 模拟测试数据
const testFileInfos = [
  {
    id: 1,
    title: '测试文档.pdf',
    type: '1', // 文档类型 - 正常预览
    downloadType: '3',
    ossId: 123,
    summary: '这是一个PDF文档的摘要信息'
  },
  {
    id: 2,
    title: '洗发水.MPJ',
    type: '2', // 其他类型文件 - 只显示文件名和下载按钮
    downloadType: '3',
    ossId: 124,
    summary: '这是一个MPJ文件'
  },
  {
    id: 3,
    title: '演示文稿.pptx',
    type: '1', // 文档类型 - 正常预览
    downloadType: '2', // 审批流下载
    ossId: 125,
    summary: 'PowerPoint演示文稿'
  },
  {
    id: 4,
    title: '数据文件.dat',
    type: '2', // 其他类型文件
    downloadType: '1', // 禁止下载
    ossId: 126,
    summary: '数据文件'
  }
];

const testOssFileInfos = [
  {
    id: 123,
    originalName: '测试文档.pdf',
    fileSize: 1024000, // 1MB
    fileSuffix: '.pdf',
    url: 'https://example.com/test.pdf'
  },
  {
    id: 124,
    originalName: '洗发水.MPJ',
    fileSize: 13824, // 13.5KB
    fileSuffix: '.MPJ',
    url: 'https://example.com/test.mpj'
  },
  {
    id: 125,
    originalName: '演示文稿.pptx',
    fileSize: 5242880, // 5MB
    fileSuffix: '.pptx',
    url: 'https://example.com/test.pptx'
  },
  {
    id: 126,
    originalName: '数据文件.dat',
    fileSize: 2048000, // 2MB
    fileSuffix: '.dat',
    url: 'https://example.com/test.dat'
  }
];

// 文件大小格式化函数（与详情页面中的函数一致）
const formatFileSize = (bytes) => {
  if (!bytes) return '';
  const sizes = ['B', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0B';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + sizes[i];
};

// 测试详情页面展示逻辑
export const testDetailPageDisplay = (fileInfo, ossFileInfo) => {
  console.log('测试文件信息:', fileInfo);
  console.log('OSS文件信息:', ossFileInfo);
  
  const result = {
    fileInfo,
    ossFileInfo,
    displayType: '',
    showRightDownloadButton: true,
    showMainDownloadButton: false,
    formattedFileSize: formatFileSize(ossFileInfo?.fileSize)
  };
  
  if (fileInfo.type === '2') {
    result.displayType = 'file-info-only';
    result.showRightDownloadButton = false; // 右侧不显示下载按钮
    result.showMainDownloadButton = true;   // 主内容区显示下载按钮
    console.log('文件类型为2，只显示文件信息和下载按钮');
  } else {
    result.displayType = 'normal-preview';
    result.showRightDownloadButton = true;  // 右侧显示下载按钮
    result.showMainDownloadButton = false;  // 主内容区不显示下载按钮
    console.log('文件类型为1或其他，正常预览模式');
  }
  
  return result;
};

// 测试下载权限逻辑
export const testDownloadPermission = (fileInfo) => {
  const { downloadType } = fileInfo;
  
  switch (downloadType) {
    case '1':
      return { canDownload: false, message: '该文件禁止下载' };
    case '2':
      return { canDownload: false, message: '需要审批流程' };
    case '3':
      return { canDownload: true, message: '可以直接下载' };
    default:
      return { canDownload: false, message: '下载类型未知' };
  }
};

// 运行所有测试
export const runDetailPageTests = () => {
  console.log('=== 开始测试详情页面展示逻辑 ===');
  
  testFileInfos.forEach((fileInfo, index) => {
    console.log(`\n--- 测试文件 ${index + 1}: ${fileInfo.title} ---`);
    
    const ossFileInfo = testOssFileInfos.find(oss => oss.id === fileInfo.ossId);
    
    // 测试展示逻辑
    const displayResult = testDetailPageDisplay(fileInfo, ossFileInfo);
    console.log('展示结果:', displayResult);
    
    // 测试下载权限
    const downloadResult = testDownloadPermission(fileInfo);
    console.log('下载权限:', downloadResult);
    
    // 输出预期的UI行为
    console.log('预期UI行为:');
    if (displayResult.displayType === 'file-info-only') {
      console.log('- 主内容区显示文件图标、名称、大小和下载按钮');
      console.log('- 右侧悬浮按钮不显示下载选项');
    } else {
      console.log('- 主内容区显示文件预览');
      console.log('- 右侧悬浮按钮显示下载选项');
    }
    
    if (downloadResult.canDownload) {
      console.log('- 下载按钮可点击');
    } else {
      console.log(`- 下载按钮点击后显示提示: ${downloadResult.message}`);
    }
  });
  
  console.log('\n=== 测试完成 ===');
};

// 导出测试函数和数据
export default {
  testDetailPageDisplay,
  testDownloadPermission,
  runDetailPageTests,
  testFileInfos,
  testOssFileInfos,
  formatFileSize
};
