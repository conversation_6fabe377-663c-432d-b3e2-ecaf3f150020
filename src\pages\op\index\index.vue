<template>
  <div class="w-[1440px] mx-auto">
    <div class="p-[24px]" style="min-height: calc(100vh - 64px)">
      <div class="grid grid-cols-3 gap-4">
        <div class="card bg-base-100 w-96 shadow-xl">
          <figure class="px-10 pt-10">
            <img
                src="https://img.daisyui.com/images/stock/photo-1494232410401-ad00d5433cfa.webp"
                alt="Shoes"
                class="rounded-xl" />
          </figure>
          <div class="card-body items-center text-center">
            <h2 class="card-title">空间名称</h2>
            <p></p>
            <div class="card-actions">
              <button class="btn btn-primary">点击进入</button>
            </div>
          </div>
        </div>
        <div class="card bg-base-100 w-96 shadow-xl">
          <figure class="px-10 pt-10">
            <img
                src="https://img.daisyui.com/images/stock/photo-1494232410401-ad00d5433cfa.webp"
                alt="Shoes"
                class="rounded-xl" />
          </figure>
          <div class="card-body items-center text-center">
            <h2 class="card-title">空间名称</h2>
            <p></p>
            <div class="card-actions">
              <button class="btn btn-primary">点击进入</button>
            </div>
          </div>
        </div>
        <div class="card bg-base-100 w-96 shadow-xl">
          <figure class="px-10 pt-10">
            <img
                src="https://img.daisyui.com/images/stock/photo-1494232410401-ad00d5433cfa.webp"
                alt="Shoes"
                class="rounded-xl" />
          </figure>
          <div class="card-body items-center text-center">
            <h2 class="card-title">空间名称</h2>
            <p></p>
            <div class="card-actions">
              <button class="btn btn-primary">点击进入</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
