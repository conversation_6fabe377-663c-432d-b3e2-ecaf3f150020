import axios, { AxiosInstance, AxiosRequestConfig } from "axios";
import { ElMessage } from "element-plus";
// @ts-ignore
import { jumpLogin, downloadFile } from "@/utils";
import {getToken} from "./auth";
// import vm from "@/main";

// let loadingInstance: ElLoadingComponent | null = null;
let requestNum = 0;

const addLoading = () => {
    // 增加loading 如果pending请求数量等于1，弹出loading, 防止重复弹出
    requestNum++;
    if (requestNum == 1) {
        // loadingInstance = Loading.service({
        //     text: "正在努力加载中....",
        //     background: "rgba(0, 0, 0, 0)",
        // });
    }
};

const cancelLoading = () => {
    // 取消loading 如果pending请求数量等于0，关闭loading
    requestNum--;
    // if (requestNum === 0) loadingInstance?.close();
};

export const createAxiosByinterceptors = (
    config?: AxiosRequestConfig
): AxiosInstance => {
    const instance = axios.create({
        timeout: 1000 * 10,    // 超时配置
        withCredentials: true,  // 跨域携带cookie
        ...config,   // 自定义配置覆盖基本配置
    });

    // 添加请求拦截器
    instance.interceptors.request.use(
        function (config: any) {
            // 在发送请求之前做些什么
            const { loading = true } = config;
            // console.log("config:", config);
            // config.headers.Authorization = vm.$Cookies.get("vue_admin_token");
            config.headers = {
                ...config.headers,
                clientId: 'e5cd7e4891bf95d1d19206ce24a7b32e',
                Authorization: 'Bearer ' + getToken(),
            }
            if (loading) addLoading();
            return config;
        },
        function (error) {
            // 对请求错误做些什么
            return Promise.reject(error);
        }
    );

    // 添加响应拦截器
    instance.interceptors.response.use(
        function (response) {
            // 对响应数据做点什么
            // console.log("response:", response);
            // const { loading = true } = response.config;
            // if (loading) cancelLoading();
            const { code, data, msg } = response.data;
            // console.log(response.data, 'response.data')
            // config设置responseType为blob 处理文件下载
            if (response.data instanceof Blob) {
                return downloadFile(response);
            } else {
                if (code === 200) {
                    return response.data
                }else if (code === 401) {
                    // ElMessage.error('token已过期，请重新登录！')
                    jumpLogin();
                }else if(code === 500) {
                    ElMessage.error(msg);
                    return Promise.reject(msg);
                } else {
                    ElMessage.error(msg);
                    return Promise.reject(msg);
                }
            }
        },
        function (error) {
            // 对响应错误做点什么
            console.log("error-response:", error.response);
            console.log("error-config:", error.config);
            console.log("error-request:", error.request);
            const { loading = true } = error.config;
            if (loading) cancelLoading();
            if (error.code === 'ECONNABORTED' && error.message.indexOf('timeout') !== -1){
                //超时处理逻辑
                console.log('请求超时,请稍后再试');
                ElMessage.error("网络异常，请稍后重试！");
            }else if (error.response) {
                if (error.response.status === 401) {
                    jumpLogin();
                }
            }
            ElMessage.error(error?.response?.data?.message || "服务端异常");
            return Promise.reject(error);
        }
    );
    return instance;
};
